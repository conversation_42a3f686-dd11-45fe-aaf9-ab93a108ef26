#!/bin/bash

# 加载环境变量（如果存在）
if [ -f ".env" ]; then
    set -a  # 自动导出所有变量
    source .env
    set +a
    echo -e "${GREEN}已加载.env文件中的环境变量${NC}"
else
    echo -e "${YELLOW}警告：未找到.env文件${NC}"
fi

# 设置环境变量
export PYTHONPATH=$PYTHONPATH:$(pwd)

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# 检查必要的环境变量
required_vars=(
    "SECRET_KEY"
    "SMTP_PASSWORD"
    "FIRST_SUPERUSER_PASSWORD"
    "MAIL_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}错误: 环境变量 $var 未设置${NC}"
        echo "请确保.env文件中包含以下配置："
        echo "SECRET_KEY=您的JWT密钥"
        echo "SMTP_PASSWORD=您的SMTP密码"
        echo "FIRST_SUPERUSER_PASSWORD=超级管理员密码"
        echo "MAIL_PASSWORD=邮件服务密码"
        exit 1
    fi
done

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p data
mkdir -p backups
mkdir -p logs
mkdir -p uploads/avatars
mkdir -p uploads/articles

# 删除旧的数据库文件
echo "删除旧的数据库文件..."
rm -f data/sql_app.db

# 删除旧的虚拟环境（如果存在）
if [ -d "venv" ]; then
    echo "删除旧的虚拟环境..."
    rm -rf venv
fi

# 创建新的虚拟环境
echo "创建虚拟环境..."
python3.11 -m venv venv

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 升级 pip
echo "升级 pip..."
python -m pip install --upgrade pip

# 安装依赖
echo "安装依赖..."
pip install -r requirements.txt

# 数据库备份（如果存在旧数据）
if [ -f data/sql_app.db ]; then
    echo "备份现有数据库..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    cp data/sql_app.db "backups/sql_app_${timestamp}.db"
    
    # 保留最近的 5 个备份
    ls -t backups/sql_app_*.db | tail -n +6 | xargs -r rm
fi

# 设置默认环境变量（如果未设置）
export DATABASE_URL=${DATABASE_URL:-sqlite:///./data/sql_app.db}
export BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-"*"}
export PROJECT_NAME=${PROJECT_NAME:-"北斗九号用户管理系统"}
export API_V1_STR=${API_V1_STR:-/api/v1}
export ALGORITHM=${ALGORITHM:-HS256}
export ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-"30"}
export FRONTEND_URL=${FRONTEND_URL:-"https://www.9day.tech/"}

# SMTP和邮件配置
export SMTP_HOST=${SMTP_HOST:-"smtp.gmail.com"}
export SMTP_PORT=${SMTP_PORT:-587}
export SMTP_USER=${SMTP_USER:-"<EMAIL>"}
export MAIL_USERNAME=${MAIL_USERNAME:-"$SMTP_USER"}
export MAIL_FROM=${MAIL_FROM:-"$SMTP_USER"}
export MAIL_PORT=${MAIL_PORT:-"$SMTP_PORT"}
export MAIL_SERVER=${MAIL_SERVER:-"$SMTP_HOST"}
export MAIL_SSL_TLS=${MAIL_SSL_TLS:-"true"}

# 超级管理员配置
export FIRST_SUPERUSER=${FIRST_SUPERUSER:-"<EMAIL>"}

# 打印当前配置
echo -e "${GREEN}当前配置:${NC}"
echo "数据库URL: $DATABASE_URL"
echo "超级管理员: $FIRST_SUPERUSER"
echo "CORS配置: $BACKEND_CORS_ORIGINS"
echo "SMTP服务器: $SMTP_HOST:$SMTP_PORT"
echo "邮件发送者: $MAIL_FROM"

# 执行Python命令初始化数据库
echo -e "${GREEN}开始初始化数据库...${NC}"
python3 main.py

if [ $? -eq 0 ]; then
    echo -e "${GREEN}数据库初始化成功${NC}"
else
    echo -e "${RED}数据库初始化失败${NC}"
    exit 1
fi

# 设置文件权限
echo "设置文件权限..."
chmod -R 755 data
chmod -R 755 backups
chmod -R 755 logs
chmod -R 755 uploads