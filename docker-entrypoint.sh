#!/bin/bash

# 确保脚本在出错时退出
set -e

# 检测运行环境
if [ -d "/app" ]; then
    # Docker 环境
    BASE_DIR="/app"
    IS_DOCKER=true
else
    # 本地环境
    BASE_DIR="."
    IS_DOCKER=false
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "=== 北斗九号用户管理系统容器初始化 ==="

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p "${BASE_DIR}/data"
mkdir -p "${BASE_DIR}/logs"
mkdir -p "${BASE_DIR}/uploads/avatars"
mkdir -p "${BASE_DIR}/uploads/articles"
mkdir -p "${BASE_DIR}/backups"

# 检查必要的环境变量
required_vars=(
    "SECRET_KEY"
    "SMTP_PASSWORD"
    "FIRST_SUPERUSER_PASSWORD"
    "MAIL_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}错误: 环境变量 $var 未设置${NC}"
        echo "请确保以下环境变量已设置:"
        echo "- SECRET_KEY: JWT密钥"
        echo "- SMTP_PASSWORD: SMTP服务密码"
        echo "- FIRST_SUPERUSER_PASSWORD: 超级管理员密码"
        echo "- MAIL_PASSWORD: 邮件服务密码"
        exit 1
    fi
done

# 设置默认环境变量
# 基础配置
export DATABASE_URL=${DATABASE_URL:-sqlite:///./data/sql_app.db}
export BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-'*'}
export PROJECT_NAME=${PROJECT_NAME:-'北斗九号用户管理系统'}
export API_V1_STR=${API_V1_STR:-/api/v1}
export ALGORITHM=${ALGORITHM:-'HS256'}
export ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-30}
export FRONTEND_URL=${FRONTEND_URL:-'https://www.9day.tech/'}

# SMTP和邮件配置
export SMTP_HOST=${SMTP_HOST:-'smtp.gmail.com'}
export SMTP_PORT=${SMTP_PORT:-587}
export SMTP_USER=${SMTP_USER:-'<EMAIL>'}
export MAIL_USERNAME=${MAIL_USERNAME:-"$SMTP_USER"}
export MAIL_FROM=${MAIL_FROM:-"$SMTP_USER"}
export MAIL_PORT=${MAIL_PORT:-"$SMTP_PORT"}
export MAIL_SERVER=${MAIL_SERVER:-"$SMTP_HOST"}
export MAIL_SSL_TLS=${MAIL_SSL_TLS:-true}

# 超级管理员配置
export FIRST_SUPERUSER=${FIRST_SUPERUSER:-'<EMAIL>'}

# 打印当前配置
echo -e "${GREEN}当前配置:${NC}"
echo "数据库URL: ${DATABASE_URL}"
echo "超级管理员: ${FIRST_SUPERUSER}"
echo "CORS配置: ${BACKEND_CORS_ORIGINS}"
echo "SMTP服务器: ${SMTP_HOST}:${SMTP_PORT}"
echo "邮件发送者: ${MAIL_FROM}"

# 需要添加数据库备份逻辑
if [ -f "${BASE_DIR}/data/sql_app.db" ]; then
    echo "备份现有数据库..."
    timestamp=$(date +%Y%m%d_%H%M%S)
    cp "${BASE_DIR}/data/sql_app.db" "${BASE_DIR}/backups/sql_app_${timestamp}.db"
    
    # 保留最近的 5 个备份
    ls -t "${BASE_DIR}/backups/sql_app_*.db" | tail -n +6 | xargs -r rm
fi

# 初始化数据库
echo "初始化数据库..."
python3 -c "from src.db.database import init_db; init_db()"

# 设置应用权限
echo "设置应用权限..."
if [ "$IS_DOCKER" = true ]; then
    # Docker环境下设置appuser权限
    chown -R appuser:appuser "${BASE_DIR}/data"
    chown -R appuser:appuser "${BASE_DIR}/logs"
    chown -R appuser:appuser "${BASE_DIR}/uploads"
    chown -R appuser:appuser "${BASE_DIR}/backups"
else
    # 本地环境下设置当前用户权限
    chmod -R 755 "${BASE_DIR}/data"
    chmod -R 755 "${BASE_DIR}/logs"
    chmod -R 755 "${BASE_DIR}/uploads"
    chmod -R 755 "${BASE_DIR}/backups"
fi

echo "启动应用..."
exec uvicorn main:app --host 0.0.0.0 --port 4000