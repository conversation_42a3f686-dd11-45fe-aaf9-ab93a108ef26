#!/bin/bash

# 加载环境变量（如果存在）
if [ -f ".env" ]; then
    set -a  # 自动导出所有变量
    source .env
    set +a
    echo -e "${GREEN}已加载.env文件中的环境变量${NC}"
else
    echo -e "${RED}错误：缺少.env文件${NC}"
    echo "请复制.env.example文件并配置必要参数"
    exit 1
fi

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

# 检查虚拟环境是否存在
if [ ! -d "venv" ]; then
    echo -e "${RED}虚拟环境不存在，请先运行 ./init_db.sh 进行初始化${NC}"
    exit 1
fi

# 检查必要的环境变量
required_vars=(
    "SECRET_KEY"
    "SMTP_PASSWORD"
    "FIRST_SUPERUSER_PASSWORD"
    "MAIL_PASSWORD"
)

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        echo -e "${RED}错误: 环境变量 $var 未设置${NC}"
        echo "请确保以下环境变量已设置:"
        echo "- SECRET_KEY: JWT密钥"
        echo "- SMTP_PASSWORD: SMTP服务密码"
        echo "- FIRST_SUPERUSER_PASSWORD: 超级管理员密码"
        echo "- MAIL_PASSWORD: 邮件服务密码"
        exit 1
    fi
done

# 激活虚拟环境
echo "激活虚拟环境..."
source venv/bin/activate

# 安装/更新依赖
echo "检查并安装依赖..."
pip install -r requirements.txt

# 设置默认环境变量（如果未设置）
export DATABASE_URL=${DATABASE_URL:-"sqlite:///./data/sql_app.db"}
export BACKEND_CORS_ORIGINS=${BACKEND_CORS_ORIGINS:-"*"}
export PROJECT_NAME=${PROJECT_NAME:-"北斗九号用户管理系统"}
export API_V1_STR=${API_V1_STR:-"/api/v1"}
export ALGORITHM=${ALGORITHM:-"HS256"}
export ACCESS_TOKEN_EXPIRE_MINUTES=${ACCESS_TOKEN_EXPIRE_MINUTES:-"30"}
export FRONTEND_URL=${FRONTEND_URL:-"https://www.9day.tech/"}

# SMTP和邮件配置
export SMTP_HOST=${SMTP_HOST:-"smtp.gmail.com"}
export SMTP_PORT=${SMTP_PORT:-"587"}
export SMTP_USER=${SMTP_USER:-"<EMAIL>"}
export MAIL_USERNAME=${MAIL_USERNAME:-"$SMTP_USER"}
export MAIL_FROM=${MAIL_FROM:-"$SMTP_USER"}
export MAIL_PORT=${MAIL_PORT:-"$SMTP_PORT"}
export MAIL_SERVER=${MAIL_SERVER:-"$SMTP_HOST"}
export MAIL_SSL_TLS=${MAIL_SSL_TLS:-"true"}

# 超级管理员配置
export FIRST_SUPERUSER=${FIRST_SUPERUSER:-"<EMAIL>"}

# 打印当前配置
echo -e "${GREEN}当前配置:${NC}"
echo "数据库URL: $DATABASE_URL"
echo "超级管理员: $FIRST_SUPERUSER"
echo "CORS配置: $BACKEND_CORS_ORIGINS"
echo "SMTP服务器: $SMTP_HOST:$SMTP_PORT"
echo "邮件发送者: $MAIL_FROM"

# 启动应用前执行数据库初始化
echo -e "${GREEN}初始化数据库...${NC}"
python3 -c "from src.db.database import init_db; init_db()"

# 启动应用
echo -e "${GREEN}启动应用...${NC}"
uvicorn main:app --reload --host 0.0.0.0 --port 4000