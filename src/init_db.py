"""
数据库初始化脚本
用于创建数据库表和初始化基础数据
"""
import logging
import os
from sqlalchemy import create_engine, inspect
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import sessionmaker
from src.db.base_class import Base
from src.config import settings
from src.core.security import get_password_hash
from src.models.user import User
from src.schemas.user import Role, SubscriptionType, Gender

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_default_users(db_session):
    """创建默认用户"""
    try:
        # 检查是否已存在用户
        if db_session.query(User).count() > 0:
            logger.info("数据库中已存在用户，跳过默认用户创建")
            return

        # 创建超级管理员
        logger.info(f"创建超级管理员账号: {settings.FIRST_SUPERUSER}")
        super_admin = User(
            email=settings.FIRST_SUPERUSER,
            username="superadmin",
            hashed_password=get_password_hash(settings.FIRST_SUPERUSER_PASSWORD),
            role=Role.SUPERADMIN,
            permissions={"read": True, "write": True, "admin": True, "superadmin": True},
            is_active=True,
            is_verified=True,
            social_links={
                "wechat": "superadmin",
                "weibo": "superadmin",
                "qq": "123456789"
            },
            avatar="https://www.9day.tech/avatar/superadmin.png",
            bio="超级管理员",
            location="北京",
            website="https://www.9day.tech"
        )
        db_session.add(super_admin)
        logger.info(f"超级管理员账号创建成功: {settings.FIRST_SUPERUSER}")

        # 创建普通管理员
        logger.info("创建普通管理员账号...")
        admin = User(
            email="<EMAIL>",
            username="admin",
            hashed_password=get_password_hash("admin123"),
            role=Role.ADMIN,
            permissions={"read": True, "write": True, "admin": True, "superadmin": False},
            is_active=True,
            is_verified=True,
            social_links={
                "wechat": "admin",
                "qq": "987654321"
            },
            avatar="https://www.9day.tech/avatar/admin.png",
            bio="管理员",
            location="上海"
        )
        db_session.add(admin)
        logger.info("普通管理员账号创建成功: <EMAIL>")

        # 创建测试用户
        logger.info("创建测试用户账号...")
        test_user = User(
            email="<EMAIL>",
            username="test",
            hashed_password=get_password_hash("test123"),
            role=Role.USER,
            permissions={"read": True, "write": True, "admin": False, "superadmin": False},
            is_active=True,
            is_verified=True,
            social_links={
                "wechat": "test_user"
            },
            bio="测试用户",
            birth_year=1990,
            birth_month=1,
            birth_day=1,
            birth_time="12:00",
            gender=Gender.MALE
        )
        db_session.add(test_user)
        logger.info("测试用户账号创建成功: <EMAIL>")

        db_session.commit()
        logger.info("默认用户创建完成")

    except Exception as e:
        db_session.rollback()
        logger.error(f"创建默认用户失败: {str(e)}")
        raise

def init_db():
    """初始化数据库"""
    try:
        # 获取数据库 URL
        db_url = settings.DATABASE_URL
        logger.info(f"配置的数据库 URL: {db_url}")
        logger.info(f"环境变量中的数据库 URL: {os.getenv('DATABASE_URL', '未设置')}")
        
        # 确保数据目录存在
        if db_url.startswith("sqlite:///"):
            db_path = db_url.replace("sqlite:///", "")
            abs_db_path = os.path.abspath(db_path)
            db_dir = os.path.dirname(abs_db_path)
            
            logger.info(f"数据库文件相对路径: {db_path}")
            logger.info(f"数据库文件绝对路径: {abs_db_path}")
            logger.info(f"数据库目录: {db_dir}")
            logger.info(f"当前工作目录: {os.getcwd()}")
            
            if db_dir:
                os.makedirs(db_dir, exist_ok=True)
                logger.info(f"确保数据库目录存在: {db_dir}")
        
        # 创建数据库引擎
        engine = create_engine(
            db_url,
            connect_args={"check_same_thread": False}  # SQLite专用参数
        )
        
        # 检查数据库连接
        try:
            engine.connect()
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            raise
        
        # 创建所有表
        logger.info("创建数据库表...")
        Base.metadata.create_all(bind=engine)
        logger.info("数据库表创建完成")
        
        # 创建会话
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 创建默认用户
            create_default_users(db)
            logger.info("数据库初始化完成")
        finally:
            db.close()
            
    except SQLAlchemyError as e:
        logger.error(f"数据库初始化失败 (SQLAlchemy): {str(e)}")
        raise
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise

if __name__ == "__main__":
    init_db()