import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from src.config import settings
import logging

logger = logging.getLogger(__name__)

def send_reset_email(email: str, token: str):
    """发送密码重置邮件"""
    try:
        msg = MIMEMultipart('alternative')  # 改用 alternative 格式
        msg['From'] = f"北斗九号日历系统管理团队 <{settings.SMTP_USER}>"  # 添加发件人名称
        msg['To'] = email
        msg['Subject'] = "密码重置请求 - 北斗九号日历系统"  # 更明确的主题
        
        # 使用配置中的前端URL
        reset_link = f"{settings.FRONTEND_URL}/reset-password?token={token}"
        print(f"生成的重置链接: {reset_link}")  # 添加调试日志
        logger.info(f"生成的重置链接: {reset_link}")  # 使用logger替代print
        
        # HTML 版本的邮件内容
        html_content = f"""
        <html>
            <body>
                <h2>密码重置请求</h2>
                <p>您好，</p>
                <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
                <p><a href="{reset_link}">{reset_link}</a></p>
                <p>此链接将在{settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS}小时后失效。</p>
                <p>如果您没有请求重置密码，请忽略此邮件。</p>
                <br>
                <p>祝好，</p>
                <p>系统管理团队</p>
            </body>
        </html>
        """
        
        # 纯文本版本
        text_content = f"""
        您好，

        我们收到了您的密码重置请求。请点击下面的链接重置您的密码：

        {reset_link}

        此链接将在{settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS}小时后失效。

        如果您没有请求重置密码，请忽略此邮件。

        祝好，
        北斗九号日历系统管理团队
        """
        
        # 添加纯文本和HTML两个版本
        msg.attach(MIMEText(text_content, 'plain'))
        msg.attach(MIMEText(html_content, 'html'))
        
        logger.info(f"正在发送重置邮件到: {email}")
        server = smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT)
        server.starttls()
        server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)
        server.send_message(msg)
        server.quit()
        logger.info(f"重置邮件发送成功: {email}")
        
    except Exception as e:
        logger.error(f"发送重置邮件失败: {str(e)}")
        raise Exception(f"发送邮件失败: {str(e)}") 