"""
数据库管理模块
包含数据库初始化和更新功能
注意：登录时使用邮箱作为登录名，而不是用户名
"""
from sqlalchemy.orm import Session
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta

from src.crud.user import get_user_by_email, create_user
from src.schemas.user import UserCreate, Role, SubscriptionType, Gender, SocialLinks
from src.config import settings
from src.core.security import get_password_hash
from src.models.user import User

def init_db(db: Session) -> None:
    """
    初始化数据库，创建超级管理员和普通管理员账号
    注意：所有用户都使用邮箱进行登录
    """
    # 检查是否已存在超级管理员
    super_admin = db.query(User).filter(User.role == Role.SUPERADMIN.value).first()
    if not super_admin:
        print("创建超级管理员账号...")
        super_admin_data = UserCreate(
            email=settings.FIRST_SUPERUSER,
            username="superadmin",
            password=settings.FIRST_SUPERUSER_PASSWORD,
            role=Role.SUPERADMIN,
            subscription_type=SubscriptionType.YEARLY,
            social_links=SocialLinks(
                wechat="superadmin",
                weibo="superadmin",
                qq="123456789"
            )
        )
        super_admin_obj = User(
            email=super_admin_data.email,
            username=super_admin_data.username,
            hashed_password=get_password_hash(super_admin_data.password),
            role=super_admin_data.role,
            subscription_features={"basic": True, "premium": True, "admin": True},
            is_active=True,
            is_verified=True,
            subscription_type=super_admin_data.subscription_type,
            permissions={"read": True, "write": True, "admin": True, "superadmin": True},
            social_links=super_admin_data.social_links.model_dump(),  # 转换为字典
            avatar="https://www.9day.tech/avatar/superadmin.png",
            bio="超级管理员",
            location="北京",
            website="https://www.9day.tech"
        )
        db.add(super_admin_obj)
        db.commit()
        print(f"超级管理员账号创建成功: {super_admin_data.email}")

    # 检查是否已存在普通管理员
    admin = db.query(User).filter(User.role == Role.ADMIN.value).first()
    if not admin:
        print("创建普通管理员账号...")
        admin_data = UserCreate(
            email="<EMAIL>",
            username="admin",
            password="admin123",
            role=Role.ADMIN,
            subscription_type=SubscriptionType.MONTHLY,
            social_links=SocialLinks(
                wechat="admin",
                qq="987654321"
            )
        )
        admin_obj = User(
            email=admin_data.email,
            username=admin_data.username,
            hashed_password=get_password_hash(admin_data.password),
            role=admin_data.role,
            subscription_features={"basic": True, "premium": True, "admin": True},
            is_active=True,
            is_verified=True,
            subscription_type=admin_data.subscription_type,
            permissions={"read": True, "write": True, "admin": True, "superadmin": False},
            social_links=admin_data.social_links.model_dump(),  # 转换为字典
            avatar="https://www.9day.tech/avatar/admin.png",
            bio="管理员",
            location="上海"
        )
        db.add(admin_obj)
        db.commit()
        print(f"普通管理员账号创建成功: {admin_data.email}")

    # 检查是否已存在测试用户
    test_user = db.query(User).filter(User.email == "<EMAIL>").first()
    if not test_user:
        print("创建测试用户账号...")
        user_data = UserCreate(
            email="<EMAIL>",
            username="test",
            password="test123",
            role=Role.USER,
            subscription_type=SubscriptionType.NONE,
            social_links=SocialLinks(
                wechat="test_user"
            )
        )
        user_obj = User(
            email=user_data.email,
            username=user_data.username,
            hashed_password=get_password_hash(user_data.password),
            role=user_data.role,
            subscription_features={"basic": True, "premium": False, "admin": False},
            is_active=True,
            is_verified=True,
            subscription_type=user_data.subscription_type,
            permissions={"read": True, "write": True, "admin": False, "superadmin": False},
            social_links=user_data.social_links.model_dump(),  # 转换为字典
            bio="测试用户",
            birth_year=1990,
            birth_month=1,
            birth_day=1,
            birth_time="12:00",
            gender=Gender.MALE
        )
        db.add(user_obj)
        db.commit()
        print(f"测试用户账号创建成功: {user_data.email}")

    db.commit()
    print("数据库初始化完成")

def update_db() -> None:
    """
    更新数据库结构，添加用户扩展字段
    """
    # 创建数据库连接
    engine = create_engine(settings.DATABASE_URL)
    
    # 定义要添加的列
    new_columns = [
        # 用户扩展信息
        "avatar VARCHAR",           # 头像URL
        "bio TEXT",                # 个人简介
        "location VARCHAR",        # 所在地
        "website VARCHAR",         # 个人网站
        
        # 社交媒体信息
        "social_links JSON",       # 社交媒体链接
        
        # 八字信息
        "birth_year INTEGER",      # 出生年
        "birth_month INTEGER",     # 出生月
        "birth_day INTEGER",       # 出生日
        "birth_time VARCHAR",      # 出生时辰
        "gender VARCHAR"           # 性别
    ]
    
    try:
        # 连接数据库
        with engine.connect() as connection:
            # 检查每个列是否存在，如果不存在则添加
            for column_def in new_columns:
                column_name = column_def.split()[0]
                # 检查列是否存在
                result = connection.execute(text(
                    f"SELECT COUNT(*) FROM pragma_table_info('users') WHERE name='{column_name}'"
                ))
                exists = result.scalar() > 0
                
                if not exists:
                    print(f"添加列: {column_name}")
                    # 添加新列
                    connection.execute(text(
                        f"ALTER TABLE users ADD COLUMN {column_def}"
                    ))
            
            connection.commit()
            print("数据库更新完成")
            
    except Exception as e:
        print(f"更新数据库时出错: {str(e)}")
        raise 