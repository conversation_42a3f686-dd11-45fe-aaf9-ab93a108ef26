import logging
from logging.handlers import RotatingFileHandler

# 配置日志记录器
logger = logging.getLogger("app")
logger.setLevel(logging.INFO)

# 添加详细格式
formatter = logging.Formatter(
    '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 文件日志
file_handler = RotatingFileHandler(
    'logs/app.log',
    maxBytes=1024*1024*10,  # 10MB
    backupCount=5
)
file_handler.setFormatter(formatter)
logger.addHandler(file_handler) 