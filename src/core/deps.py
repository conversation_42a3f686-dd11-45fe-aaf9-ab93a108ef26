"""
核心依赖模块
包含认证和权限相关的依赖函数
"""
from typing import Generator, Optional, Callable, List, Union
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from sqlalchemy.orm import Session
from datetime import datetime

from src.db.database import SessionLocal
from src.core.security import ALGORITHM
from src.config import settings
from src.crud.user import get_user_by_email
from src.models.user import User

oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/users/login")

def get_db() -> Generator:
    """获取数据库会话"""
    try:
        db = SessionLocal()
        yield db
    finally:
        db.close()

def get_current_user(
    db: Session = Depends(get_db),
    token: Optional[str] = Depends(OAuth2PasswordBearer(tokenUrl=f"{settings.API_V1_STR}/users/login", auto_error=False))
) -> Optional[User]:
    """获取当前用户（允许返回None）
    
    验证 JWT token 并返回当前用户
    如果未提供token或token无效则返回None
    """
    # 当未提供token时直接返回None
    if not token:
        return None

    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        email: str = payload.get("sub")
        if email is None:
            return None
    except JWTError:
        # 捕获所有JWT相关错误并返回None
        return None
        
    user = get_user_by_email(db, email)
    return user if user else None

def get_current_active_user(current_user: Optional[User] = Depends(get_current_user)) -> User:
    """获取当前活跃用户"""
    # 显式检查用户是否存在 ✅ 重要安全边界
    if not current_user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,  # 保持401状态码
            detail="需要登录才能访问该资源",
            headers={"WWW-Authenticate": "Bearer"}
        )
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户已被禁用"
        )
    return current_user

def get_current_verified_user(current_user: User = Depends(get_current_active_user)) -> User:
    """获取当前已验证用户
    
    检查用户是否已通过验证
    """
    if not current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户邮箱未验证"
        )
    return current_user

def check_user_role(allowed_roles: List[str]) -> Callable:
    """创建角色检查依赖
    
    Args:
        allowed_roles: 允许的角色列表
    
    Returns:
        依赖函数，用于检查用户是否具有允许的角色
    """
    def role_checker(current_user: User = Depends(get_current_active_user)) -> User:
        if current_user.role not in allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"需要以下角色之一的权限: {', '.join(allowed_roles)}"
            )
        return current_user
    return role_checker

def check_permissions(required_permissions: Union[str, List[str]], require_all: bool = True) -> Callable:
    """创建权限检查依赖
    
    Args:
        required_permissions: 需要的权限或权限列表
        require_all: 是否需要满足所有权限，默认为True。
                    如果为False，则只需满足其中任意一个权限。
    
    Returns:
        依赖函数，用于检查用户是否具有所需权限
    """
    if isinstance(required_permissions, str):
        required_permissions = [required_permissions]
    
    def permission_checker(current_user: User = Depends(get_current_active_user)) -> User:
        user_permissions = current_user.permissions
        
        # 超级管理员拥有所有权限
        if current_user.role == "superadmin":
            return current_user
            
        if require_all:
            # 检查是否具有所有需要的权限
            missing_permissions = [
                perm for perm in required_permissions 
                if not user_permissions.get(perm, False)
            ]
            if missing_permissions:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"缺少以下权限: {', '.join(missing_permissions)}"
                )
        else:
            # 检查是否具有任意一个需要的权限
            has_any_permission = any(
                user_permissions.get(perm, False) 
                for perm in required_permissions
            )
            if not has_any_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要以下权限之一: {', '.join(required_permissions)}"
                )
        
        return current_user
    return permission_checker

def check_subscription_features(required_features: Union[str, List[str]], require_all: bool = True) -> Callable:
    """创建订阅功能检查依赖
    
    Args:
        required_features: 需要的功能或功能列表
        require_all: 是否需要满足所有功能，默认为True。
                    如果为False，则只需满足其中任意一个功能。
    
    Returns:
        依赖函数，用于检查用户是否具有所需的订阅功能
    """
    if isinstance(required_features, str):
        required_features = [required_features]
    
    def feature_checker(current_user: User = Depends(get_current_active_user)) -> User:
        user_features = current_user.subscription_features
        
        # 超级管理员拥有所有功能
        if current_user.role == "superadmin":
            return current_user
            
        if require_all:
            # 检查是否具有所有需要的功能
            missing_features = [
                feat for feat in required_features 
                if not user_features.get(feat, False)
            ]
            if missing_features:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"订阅计划不包含以下功能: {', '.join(missing_features)}"
                )
        else:
            # 检查是否具有任意一个需要的功能
            has_any_feature = any(
                user_features.get(feat, False) 
                for feat in required_features
            )
            if not has_any_feature:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"订阅计划需要包含以下功能之一: {', '.join(required_features)}"
                )
        
        return current_user
    return feature_checker

# 预定义的角色检查依赖
get_current_admin = check_user_role(["admin", "superadmin"])
get_current_superuser = check_user_role(["superadmin"])

# 预定义的权限检查依赖
require_read = check_permissions("read")
require_write = check_permissions("write")
require_admin = check_permissions("admin")

# 预定义的订阅功能检查依赖
require_basic = check_subscription_features("basic")
require_premium = check_subscription_features("premium")
require_admin_features = check_subscription_features("admin")

def get_current_active_admin(current_user: User = Depends(get_current_user)) -> User:
    """验证管理员权限"""
    if not current_user or current_user.role not in {"admin", "superadmin"}:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    return current_user 