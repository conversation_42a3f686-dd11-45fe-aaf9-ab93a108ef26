"""
备份相关的API路由处理函数
提供数据库和数据表的备份与恢复功能
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
import json
from datetime import datetime
import os
import zipfile
import logging
from sqlalchemy.exc import SQLAlchemyError
from pathlib import Path
import io
import shutil
import sqlite3
import aiofiles

from src.core.deps import get_current_admin, get_current_superuser, get_db
from src.crud import backup as crud
from src.schemas.backup import DatabaseBackup, BackupData
from src.config import settings
from src.models.user import User  # 从模型层导入数据库模型
from src.schemas.user import UserOut  # 导入输出模型
from src.core.deps import get_current_active_admin

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/download")
async def download_database(
    current_user: User = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """下载原始数据库文件（保持原格式）"""
    try:
        # 获取数据库文件路径
        db_path = settings.DATABASE_URL.replace("sqlite:///", "")
        if not os.path.exists(db_path):
            raise HTTPException(404, "数据库文件不存在")
        
        # 创建带时间戳的备份文件
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        backup_name = f"db_backup_{timestamp}.db"
        backup_path = os.path.join(settings.BASE_DIR, "temp", backup_name)
        
        # 复制数据库文件
        shutil.copy2(db_path, backup_path)
        
        return FileResponse(
            path=backup_path,
            filename=backup_name,
            media_type="application/x-sqlite3",
            headers={
                "Content-Disposition": f"attachment; filename={backup_name}",
                "X-Backup-Type": "database-file"
            }
        )
        
    except Exception as e:
        logger.error(f"数据库下载失败: {str(e)}")
        raise HTTPException(500, detail="数据库下载失败")

@router.post("/upload")
async def upload_database(
    # 参数名与前端上传字段名保持一致
    backup_file: UploadFile = File(..., 
        description="数据库备份文件(.db/.zip)", 
        alias="backup_file"  # 显式声明字段别名
    ),
    current_user: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """上传并恢复数据库备份（仅超级管理员）"""
    try:
        # 验证文件类型（兼容前端上传的.zip格式）
        if not backup_file.filename.lower().endswith(('.db', '.zip')):
            raise HTTPException(400, "仅支持.db或.zip格式的备份文件")
        
        # 统一处理不同格式的备份文件
        if backup_file.filename.endswith('.zip'):
            return await handle_zip_backup(backup_file, current_user, db)
        else:
            return await handle_db_file(backup_file, current_user, db)
            
    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"备份恢复失败: {str(e)}", exc_info=True)
        raise HTTPException(500, detail=f"备份恢复失败: {str(e)}")

@router.get("/export")
async def export_tables(
    current_user = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    导出数据表（仅管理员）
    返回一个包含所有表数据的 JSON 文件
    """
    try:
        # 导出用户数据
        users = crud.export_users(db)
        
        # 创建临时目录
        temp_dir = os.path.join(settings.BASE_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 生成导出文件名
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        export_name = f"tables_export_{timestamp}.json"
        export_path = os.path.join(temp_dir, export_name)
        
        # 保存数据到文件
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        
        # 直接返回 JSON 数据
        return users

    except Exception as e:
        logger.error(f"导出数据表失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"导出数据表失败: {str(e)}"
        )

@router.get("/export/download")
async def download_export(
    current_user = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    下载导出的数据表（仅管理员）
    """
    try:
        # 导出用户数据
        users = crud.export_users(db)
        
        # 创建临时目录
        temp_dir = os.path.join(settings.BASE_DIR, "temp")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 生成导出文件名
        timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
        export_name = f"tables_export_{timestamp}.json"
        export_path = os.path.join(temp_dir, export_name)
        
        # 保存数据到文件
        with open(export_path, 'w', encoding='utf-8') as f:
            json.dump(users, f, ensure_ascii=False, indent=2)
        
        # 设置响应头
        headers = {
            "Content-Disposition": f'attachment; filename="{export_name}"',
            "Access-Control-Expose-Headers": "Content-Disposition",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
            "Access-Control-Allow-Headers": "Content-Type, Authorization"
        }
        
        # 返回文件响应
        return FileResponse(
            path=export_path,
            filename=export_name,
            media_type="application/json",
            headers=headers
        )
    except Exception as e:
        logger.error(f"下载导出文件失败: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"下载导出文件失败: {str(e)}"
        )

async def validate_backup_file(file: UploadFile):
    """增强版文件验证（异步版本）"""
    # 允许的文件扩展名和MIME类型
    ALLOWED_EXTENSIONS = {'.json', '.JSON', '.zip', '.ZIP'}
    
    # 验证文件后缀
    if not any(file.filename.endswith(ext) for ext in ALLOWED_EXTENSIONS):
        raise HTTPException(400, "不支持的文件类型，请上传JSON文件(.json)或ZIP压缩包(.zip)")

    # 读取文件头（使用异步读取）
    header_bytes = await file.read(4)
    await file.seek(0)  # 重置文件指针
    
    try:
        header = header_bytes.decode('utf-8', errors='ignore')
    except UnicodeDecodeError:
        header = str(header_bytes)

    # 通过文件头验证类型
    if not (header.startswith(('PK', '{', '[')) or header in ('%PDF', '\x25\x50\x44\x46')):
        raise HTTPException(400, "无效的文件格式，支持JSON或包含JSON的ZIP文件")

@router.post("/import")
async def import_backup(
    backup_file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    try:
        await validate_backup_file(backup_file)  # 异步验证
        
        # 处理压缩文件
        if backup_file.filename.endswith('.zip'):
            return await handle_zip_backup(backup_file, current_user, db)
            
        # 添加时间戳记录
        start_time = datetime.utcnow()
        logger.info(f"开始导入备份 {start_time.isoformat()}")
        
        # 读取并解析文件内容
        contents = await backup_file.read()
        backup_data = json.loads(contents)
        
        # 处理所有历史版本的时间字段
        time_fields = ['backup_time', 'backup_date', 'timestamp', 'created_at']
        found_time_field = next((f for f in time_fields if f in backup_data), None)

        # 处理时间字段
        try:
            if found_time_field:
                time_value = backup_data[found_time_field]
                # 类型安全转换
                if isinstance(time_value, str):
                    time_value = datetime.fromisoformat(time_value)
                backup_data['backup_time'] = time_value
                del backup_data[found_time_field]  # 安全删除旧字段
        except Exception as e:
            logger.error(f"时间字段处理失败: {str(e)}", exc_info=True)
            raise HTTPException(400, detail=f"无效时间格式: {found_time_field}")

        # 创建Pydantic模型实例
        validated_data = BackupData(**backup_data)

        # 处理用户数据导入
        success_count = 0
        for user_data in validated_data.users:
            try:
                user_dict = user_data.model_dump(
                    exclude={"created_at"},  # 仅排除创建时间
                    exclude_unset=True
                )
                
                # 根据邮箱查找现有用户
                existing_user = db.query(User).filter(
                    User.email == user_dict["email"]
                ).first()
                
                if existing_user:
                    # 更新现有用户（保留原ID）
                    for key, value in user_dict.items():
                        setattr(existing_user, key, value)
                    db.merge(existing_user)
                else:
                    # 创建新用户（自动生成ID）
                    new_user = User(**user_dict)
                    db.add(new_user)
                    
                success_count += 1
                
            except Exception as e:
                logger.error(f"用户数据导入失败: {str(e)}")
                continue

        db.commit()
        
        # 添加导入结果日志
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds()
        logger.info(
            f"备份导入完成，耗时{duration:.2f}秒，"
            f"成功导入{success_count}条用户数据"
        )
        
        return {
            "message": f"成功导入{success_count}/{len(validated_data.users)}条用户数据",
            "backup_version": validated_data.version,
            "backup_time": validated_data.backup_time
        }

    except HTTPException as he:
        # 直接传递原始异常
        raise he
    except Exception as e:
        logger.error(f"未处理的异常: {str(e)}", exc_info=True)
        raise HTTPException(500, detail="服务器内部错误")

async def handle_db_file(
    db_file: UploadFile, 
    user: User, 
    db: Session
):
    """处理直接上传的.db文件"""
    # 原有数据库替换逻辑
    # ... [保持原有upload_database中的数据库替换逻辑] ...

async def handle_zip_backup(
    zip_file: UploadFile, 
    user: User, 
    db: Session
):
    """处理ZIP压缩的数据库文件"""
    try:
        with zipfile.ZipFile(io.BytesIO(await zip_file.read())) as zf:
            # 查找.db文件
            db_files = [name for name in zf.namelist() if name.endswith('.db')]
            if not db_files:
                raise HTTPException(400, "ZIP文件中未找到数据库文件")
            
            # 解压第一个.db文件
            with zf.open(db_files[0]) as db_file:
                return await handle_db_file(
                    UploadFile(
                        filename=db_files[0], 
                        file=io.BytesIO(db_file.read())
                    ), 
                    user, 
                    db
                )
    except zipfile.BadZipFile:
        raise HTTPException(400, "无效的ZIP文件格式") 