"""
八字分析相关的 API 路由
所有操作都基于当前登录用户的权限
"""
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Path, Query
from sqlalchemy.orm import Session
from src.core.deps import get_current_user, get_db
from src.crud import bazi_analysis
from src.schemas.bazi_analysis import (
    BaziAnalysisCreate,
    BaziAnalysisRead,
    BaziAnalysisUpdate,
    BaziAnalysisList
)

router = APIRouter()

@router.post(
    "",
    response_model=BaziAnalysisRead,
    status_code=status.HTTP_201_CREATED,
    summary="创建八字分析",
    description="创建新的八字分析记录。需要提供出生年月日等基本信息，分析结果将由系统生成。"
)
def create_analysis(
    analysis: BaziAnalysisCreate,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    创建新的八字分析记录
    
    - 必须提供出生年月日
    - 出生时间为可选项
    - 可以添加分析类型和备注信息
    - 分析结果将由系统生成
    """
    return bazi_analysis.create_bazi_analysis(
        db=db,
        current_user=current_user,
        analysis=analysis
    )

@router.get(
    "",
    response_model=BaziAnalysisList,
    summary="获取八字分析列表",
    description="获取八字分析记录列表。普通用户只能看到自己的分析，管理员可以看到所有分析。"
)
def list_analyses(
    skip: int = Query(0, ge=0, description="分页起始位置"),
    limit: int = Query(100, ge=1, le=100, description="每页记录数"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取八字分析列表
    
    - 支持分页查询
    - 普通用户只能看到自己的分析
    - 管理员可以看到所有分析
    - 按创建时间倒序排列
    """
    analyses = bazi_analysis.get_user_bazi_analyses(
        db=db,
        current_user=current_user,
        skip=skip,
        limit=limit
    )
    total = bazi_analysis.get_analyses_count(db, current_user)
    return {
        "total": total,
        "items": analyses
    }

@router.get(
    "/{analysis_id}",
    response_model=BaziAnalysisRead,
    summary="获取指定八字分析",
    description="获取指定ID的八字分析记录。普通用户只能查看自己的分析，管理员可以查看所有分析。"
)
def get_analysis(
    analysis_id: int = Path(..., title="分析ID", description="要查询的八字分析记录ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    获取指定的八字分析记录
    
    - 需要提供分析记录ID
    - 普通用户只能查看自己的分析
    - 管理员可以查看所有分析
    """
    analysis = bazi_analysis.get_bazi_analysis(
        db=db,
        analysis_id=analysis_id,
        current_user=current_user
    )
    if not analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析记录不存在或无权访问"
        )
    return analysis

@router.put(
    "/{analysis_id}",
    response_model=BaziAnalysisRead,
    summary="更新八字分析",
    description="更新指定ID的八字分析记录。普通用户只能更新自己的分析，管理员可以更新所有分析。"
)
def update_analysis(
    analysis_id: int = Path(..., title="分析ID", description="要更新的八字分析记录ID"),
    analysis: BaziAnalysisUpdate = None,
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新八字分析记录
    
    - 可以更新分析类型和备注信息
    - 普通用户只能更新自己的分析
    - 管理员可以更新所有分析
    - 分析结果字段由系统生成，不能直接修改
    """
    return bazi_analysis.update_bazi_analysis(
        db=db,
        current_user=current_user,
        analysis_id=analysis_id,
        analysis=analysis
    )

@router.delete(
    "/{analysis_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除八字分析",
    description="删除指定ID的八字分析记录。普通用户只能删除自己的分析，管理员可以删除所有分析。"
)
def delete_analysis(
    analysis_id: int = Path(..., title="分析ID", description="要删除的八字分析记录ID"),
    current_user = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    删除八字分析记录
    
    - 需要提供分析记录ID
    - 普通用户只能删除自己的分析
    - 管理员可以删除所有分析
    - 删除后数据无法恢复
    """
    bazi_analysis.delete_bazi_analysis(
        db=db,
        current_user=current_user,
        analysis_id=analysis_id
    )