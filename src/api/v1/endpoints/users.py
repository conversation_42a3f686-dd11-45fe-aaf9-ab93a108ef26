"""
用户相关的API路由处理函数
包含用户注册、登录、信息管理等功能
"""
from typing import List, Optional
from datetime import timedelta, datetime
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from fastapi.security import OAuth2PasswordRequestForm, OAuth2PasswordBearer
from sqlalchemy.orm import Session
from src.utils.email import send_reset_email
import secrets
import logging
import aiofiles
import os

from src.core.deps import (
    get_db, get_current_user, get_current_active_user,
    get_current_admin, get_current_superuser
)
from src.config import settings
from src.core.security import create_access_token
from src.crud import user as crud
from src.crud import password_reset as reset_crud
from src.schemas.user import (
    UserCreate,  # 用户创建模型
    UserResponse,  # 用户响应模型
    UserUpdate,  # 用户更新模型
    UserDB,  # 数据库模型
    Token, UserStats,
    UserPermissions, SubscriptionUpdate
)
from src.schemas.password_reset import (
    PasswordResetRequest,
    PasswordResetResponse,
    PasswordResetVerify,
    PasswordResetToken
)
from src.models.user import User as UserModel

# 添加这个常量定义
TOKEN_EXPIRY_HOURS = 24  # 密码重置令牌有效期（小时）

router = APIRouter()

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/users/login")

# 基础认证接口
@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
def register(user: UserCreate, db: Session = Depends(get_db)):
    """用户注册"""
    db_user = crud.get_user_by_email(db, user.email)
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    return crud.create_user(db, user)

# 注册路由别名
@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED, include_in_schema=False)
def register_alias(user: UserCreate, db: Session = Depends(get_db)):
    """注册路由别名（兼容旧路径）"""
    return register(user, db)

@router.post("/login", response_model=Token)
def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """用户登录
    
    使用邮箱和密码进行登录
    - form_data.username 字段实际上需要填写邮箱地址
    - form_data.password 字段填写密码
    
    示例:
        username: <EMAIL>  # 填写邮箱
        password: 123456           # 填写密码
    """
    user = crud.authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="邮箱或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户已被禁用",
            headers={"WWW-Authenticate": "Bearer"},
        )
    access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email, "role": user.role},
        expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer"}

# 登录路由别名
@router.post("/login", response_model=Token, include_in_schema=False)
def login_alias(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """登录路由别名（兼容旧路径）"""
    return login(form_data, db)

# 密码重置相关接口
@router.post("/password/reset-request", response_model=PasswordResetResponse)
async def request_password_reset(
    request: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """请求密码重置"""
    user = crud.get_user_by_email(db, request.email)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    # 生成重置令牌
    reset_token = secrets.token_urlsafe(32)
    now = datetime.utcnow()
    
    # 保存令牌信息
    token_data = PasswordResetToken(
        email=request.email,
        token=reset_token,
        created_at=now,
        expires_at=now + timedelta(hours=TOKEN_EXPIRY_HOURS)
    )
    
    reset_crud.create_reset_token(db, token_data)
    
    # 发送重置邮件
    send_reset_email(request.email, reset_token)
    
    # 清理过期的令牌
    reset_crud.cleanup_expired_tokens(db)
    
    return PasswordResetResponse(
        message="密码重置邮件已发送",
        email=request.email
    )

@router.post("/password/reset-verify", response_model=PasswordResetResponse)
async def verify_password_reset(
    verify: PasswordResetVerify,
    db: Session = Depends(get_db)
):
    """验证密码重置"""
    token_data = reset_crud.get_reset_token(db, verify.token)
    
    if not token_data:
        raise HTTPException(status_code=400, detail="无效的重置令牌")
    
    if token_data.is_used:
        raise HTTPException(status_code=400, detail="此重置令牌已被使用")
    
    if datetime.utcnow() > token_data.expires_at:
        raise HTTPException(status_code=400, detail="重置令牌已过期")
    
    # 更新用户密码
    user = crud.get_user_by_email(db, token_data.email)
    if not user:
        raise HTTPException(status_code=404, detail="用户不存在")
    
    crud.update_user_password(db, user.id, verify.new_password)
    
    # 标记令牌为已使用
    reset_crud.mark_token_as_used(db, verify.token)
    
    return PasswordResetResponse(
        message="密码重置成功",
        email=token_data.email
    )

# 密码重置路由别名（兼容旧路径）
@router.post("/password-reset", response_model=PasswordResetResponse, include_in_schema=False)
async def password_reset_request_alias(
    request: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """密码重置请求路由别名（兼容旧路径）"""
    return await request_password_reset(request, db)

@router.post("/password-reset-verify", response_model=PasswordResetResponse, include_in_schema=False)
async def password_reset_verify_alias(
    verify: PasswordResetVerify,
    db: Session = Depends(get_db)
):
    """密码重置验证路由别名（兼容旧路径）"""
    return await verify_password_reset(verify, db)

# 用户信息接口
@router.get("/me", response_model=UserResponse)
def read_user_me(
    current_user: UserModel = Depends(get_current_active_user),  # 依赖认证校验
    token: str = Depends(oauth2_scheme)  # 添加 OpenAPI 认证头
):
    """获取当前用户信息（需要认证）"""
    return current_user

@router.get("/me/permissions", response_model=UserPermissions)
def read_current_user_permissions(current_user: UserModel = Depends(get_current_active_user)):
    """获取当前用户权限信息"""
    return UserPermissions(
        role=current_user.role,
        permissions=current_user.permissions,
        subscription_features=current_user.subscription_features,
        is_active=current_user.is_active,
        is_verified=current_user.is_verified,
        subscription_type=current_user.subscription_type,
        subscription_end_date=current_user.subscription_end_date
    )

@router.put("/me", response_model=UserResponse)
def update_current_user(
    user_update: UserUpdate,
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新当前用户信息"""
    # 普通用户不能修改敏感字段
    update_data = user_update.model_dump(exclude_unset=True)
    if "role" in update_data or "permissions" in update_data:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权修改角色和权限"
        )
    return crud.update_user(db, current_user.id, user_update)

@router.post("/me/avatar", response_model=UserResponse)
async def update_user_avatar(
    file: UploadFile = File(...),
    current_user: UserModel = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """更新用户头像"""
    # 验证文件类型
    allowed_types = settings.ALLOWED_IMAGE_TYPES
    if not isinstance(allowed_types, (list, tuple)):
        allowed_types = [allowed_types]
    
    if not any(file.content_type.lower().startswith(t.lower()) for t in allowed_types):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型。支持的类型: {', '.join(allowed_types)}"
        )
    
    # 验证文件大小
    content = await file.read()
    if len(content) > settings.MAX_UPLOAD_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件大小超过限制 ({settings.MAX_UPLOAD_SIZE / 1024 / 1024:.1f}MB)"
        )
    
    try:
        # 创建用户头像目录
        avatar_dir = os.path.join(settings.UPLOAD_DIR, "avatars", str(current_user.id))
        os.makedirs(avatar_dir, exist_ok=True)
        
        # 保存文件
        file_ext = os.path.splitext(file.filename)[1].lower()
        if not file_ext:
            file_ext = ".jpg"  # 默认扩展名
        
        avatar_path = os.path.join(avatar_dir, f"avatar{file_ext}")
        async with aiofiles.open(avatar_path, "wb") as f:
            await f.write(content)
        
        # 更新用户头像URL
        avatar_url = f"/uploads/avatars/{current_user.id}/avatar{file_ext}"
        return crud.update_user(
            db,
            current_user.id,
            UserUpdate(avatar=avatar_url)
        )
    except Exception as e:
        logging.error(f"上传头像失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="上传头像失败，请稍后重试"
        )

# 管理员接口
@router.get("", response_model=List[UserResponse])
def read_users(
    skip: int = 0,
    limit: int = 100,
    role: Optional[str] = None,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户列表（仅管理员）"""
    # 非超级管理员不能查看管理员和超级管理员
    if current_user.role != "superadmin" and role in ["admin", "superadmin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权查看管理员信息"
        )
    return crud.get_users(db, skip=skip, limit=limit, role=role)

@router.get("/stats", response_model=UserStats)
def read_user_stats(
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取用户统计信息（仅管理员）"""
    return crud.get_user_stats(db)

@router.get("/{user_id}", response_model=UserResponse)
def read_user(
    user_id: int,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取指定用户信息（仅管理员）"""
    db_user = crud.get_user(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 非超级管理员不能查看管理员和超级管理员信息
    if current_user.role != "superadmin" and db_user.role in ["admin", "superadmin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权查看管理员信息"
        )
    return db_user

@router.put("/{user_id}", response_model=UserResponse)
def update_user(
    user_id: int,
    user_update: UserUpdate,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新指定用户信息（仅管理员）"""
    db_user = crud.get_user(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 转换为字典以便过滤字段
    update_data = user_update.model_dump(exclude_unset=True)
    
    # 检查权限
    if current_user.role != "superadmin":
        # 普通管理员不能修改其他管理员
        if db_user.role in ["admin", "superadmin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改管理员信息"
            )
        # 普通管理员不能将用户提升为管理员
        if update_data.get("role") in ["admin", "superadmin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权提升用户为管理员"
            )
        # 普通管理员不能修改敏感字段
        sensitive_fields = ["permissions", "subscription_type", "subscription_features", "subscription_end_date"]
        if any(field in update_data for field in sensitive_fields):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改用户权限和订阅信息"
            )
    
    # 创建新的 UserUpdate 对象，只包含允许的字段
    filtered_update = UserUpdate(**update_data)
    return crud.update_user(db, user_id, filtered_update)

@router.put("/{user_id}/subscription", response_model=UserResponse)
def update_user_subscription(
    user_id: int,
    subscription: SubscriptionUpdate,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """更新用户订阅信息（仅管理员）"""
    db_user = crud.get_user(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 非超级管理员不能修改管理员的订阅信息
    if current_user.role != "superadmin":
        if db_user.role in ["admin", "superadmin"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改管理员的订阅信息"
            )
        # 普通管理员不能修改高级订阅功能
        update_data = subscription.model_dump(exclude_unset=True)
        if "subscription_features" in update_data:
            admin_features = ["admin", "superadmin"]
            if any(feature in admin_features for feature in update_data["subscription_features"]):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权修改高级订阅功能"
                )
    
    return crud.update_subscription(db, user_id, subscription)

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_user(
    user_id: int,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """删除指定用户（仅管理员）"""
    db_user = crud.get_user(db, user_id)
    if not db_user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    # 只有超级管理员可以删除管理员
    if current_user.role != "superadmin" and db_user.role in ["admin", "superadmin"]:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有超级管理员可以删除管理员账户"
        )
    
    crud.delete_user(db, user_id)

# 明确导出路由实例
__all__ = ["router"]