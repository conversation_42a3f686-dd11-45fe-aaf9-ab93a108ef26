"""
文章相关的 API 路由处理函数
包含文章的 CRUD 操作和图片上传
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Query
from sqlalchemy.orm import Session
import os

from src.core.deps import get_current_user, get_current_admin
from src.crud import article as crud
from src.schemas.article import (
    ArticleCreate, ArticleUpdate, ArticleRead,
    ArticleList, ArticleStats, ArticleImageRead
)
from src.db.database import get_db
from src.models.user import User as UserModel
from src.config import settings

router = APIRouter()

@router.post("", response_model=ArticleRead)
async def create_article(
    article: ArticleCreate,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    创建新文章（仅管理员）
    - **title**: 必填，文章标题（至少1个字符，示例："如何学习Python编程"）
    - **content**: 必填，文章内容（至少50个字符，示例："Python是一门优秀的编程语言..."）
    - **slug**: 可选，URL标识（自动处理重复情况，示例："learn-python"）
    - **status**: 可选，默认为草稿（draft/published/archived）
    """
    # 验证文章状态
    if article.status not in crud.ARTICLE_STATUS.values():
        raise HTTPException(
            status_code=400,
            detail=f"无效的文章状态，可选值: {', '.join(crud.ARTICLE_STATUS.values())}"
        )
    return crud.create_article(db, article, current_user.id)

@router.get("", response_model=ArticleList)
async def read_articles(
    skip: int = 0,
    limit: int = 10,
    category: Optional[str] = None,
    status: Optional[str] = None,
    author_id: Optional[int] = None,
    tag: Optional[str] = None,
    search: Optional[str] = None,
    order_by: str = Query("created_at", regex="^(created_at|updated_at|view_count|like_count)$"),
    current_user: Optional[UserModel] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取文章列表
    
    未登录用户和普通用户只能看到已发布的文章
    管理员可以看到所有文章
    """
    # 优化权限判断逻辑
    is_admin = current_user and current_user.role in {"admin", "superadmin"}
    
    # 验证状态参数
    if status and status not in crud.ARTICLE_STATUS.values():
        raise HTTPException(
            status_code=400,
            detail=f"无效的文章状态，可选值: {', '.join(crud.ARTICLE_STATUS.values())}"
        )
    
    # 强制非管理员用户只能查看已发布文章
    if not is_admin:
        status = crud.ARTICLE_STATUS["PUBLISHED"]
    
    return crud.get_articles(
        db, skip, limit, category, status,
        author_id, tag, search, order_by,
        is_admin=is_admin
    )

@router.get("/stats", response_model=ArticleStats)
async def get_article_stats(
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """获取文章统计信息（仅管理员）"""
    return crud.get_article_stats(db)

@router.get("/{article_id}", response_model=ArticleRead)
async def read_article(
    article_id: int,
    increment_views: bool = True,
    current_user: Optional[UserModel] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定文章
    
    普通用户只能看到已发布的文章
    管理员可以看到所有文章
    
    Args:
        increment_views: 是否增加浏览量，默认为True
    """
    article = crud.get_article_with_view_count(db, article_id, increment_views)
    if article is None:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    # 检查权限
    is_admin = current_user and current_user.role in ["admin", "superadmin"] if current_user else False
    if not is_admin and article.status != crud.ARTICLE_STATUS["PUBLISHED"]:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    return article

@router.get("/by-slug/{slug}", response_model=ArticleRead)
async def read_article_by_slug(
    slug: str,
    increment_views: bool = True,
    current_user: Optional[UserModel] = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """通过 slug 获取文章
    
    普通用户只能看到已发布的文章
    管理员可以看到所有文章
    
    Args:
        increment_views: 是否增加浏览量，默认为True
    """
    article = crud.get_article_by_slug(db, slug)
    if article is None:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    # 检查权限
    is_admin = current_user and current_user.role in ["admin", "superadmin"] if current_user else False
    if not is_admin and article.status != crud.ARTICLE_STATUS["PUBLISHED"]:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    # 增加浏览量
    if increment_views and article.status == crud.ARTICLE_STATUS["PUBLISHED"]:
        crud.increment_view_count(db, article.id)
    
    return article

@router.put("/{article_id}", response_model=ArticleRead)
async def update_article(
    article_id: int,
    article_update: ArticleUpdate,
    current_user: UserModel = Depends(get_current_admin),
    db: Session = Depends(get_db)
):
    """
    更新文章（仅管理员）
    - 允许状态转换：草稿->发布/归档， 发布->归档/草稿， 归档->草稿
    - 发布时会自动设置发布时间
    - 归档会清除发布时间
    """
    # 检查文章是否存在
    db_article = crud.get_article(db, article_id)
    if db_article is None:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    # 验证文章状态
    if article_update.status and article_update.status not in crud.ARTICLE_STATUS.values():
        raise HTTPException(
            status_code=400,
            detail=f"无效的文章状态，可选值: {', '.join(crud.ARTICLE_STATUS.values())}"
        )
    
    return crud.update_article(db, article_id, article_update)

@router.delete("/{article_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_article(
    article_id: int,
    current_user: UserModel = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除文章"""
    # 检查文章是否存在
    db_article = crud.get_article(db, article_id)
    if db_article is None:
        raise HTTPException(status_code=404, detail="文章不存在")
    
    # 检查权限
    if db_article.author_id != current_user.id and current_user.role not in ["admin", "superadmin"]:
        raise HTTPException(status_code=403, detail="没有权限删除此文章")
    
    crud.delete_article(db, article_id)

@router.post("/upload", response_model=ArticleImageRead)
async def upload_image(
    file: UploadFile = File(...),
    article_id: Optional[int] = None,
    current_user: UserModel = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """上传图片
    
    如果提供了 article_id，图片将关联到指定文章
    否则创建独立的图片记录
    """
    # 验证文件类型
    if not file.content_type.startswith('image/'):
        raise HTTPException(status_code=400, detail="只能上传图片文件")
    
    # 如果指定了文章，检查权限
    if article_id:
        article = crud.get_article(db, article_id)
        if article is None:
            raise HTTPException(status_code=404, detail="文章不存在")
        if article.author_id != current_user.id and current_user.role not in ["admin", "superadmin"]:
            raise HTTPException(status_code=403, detail="没有权限为此文章上传图片")
    
    # 保存文件
    upload_dir = os.path.join(settings.UPLOAD_DIR, "articles")
    file_info = await crud.save_upload_file(file, upload_dir)
    
    # 创建图片记录
    image = crud.create_article_image(db, article_id, file_info)
    return image 