"""
API v1 版本路由管理
统一管理所有 v1 版本的 API 路由
"""
from fastapi import APIRouter, Response, status
from fastapi.middleware.cors import CORSMiddleware

from src.api.v1.endpoints import users, backup, articles, bazi_analysis  # 移除 subscriptions
from src.api.v1.endpoints.users import router as users_router
from src.schemas.user import UserResponse

# 创建主路由
api_router = APIRouter()

# 创建根路由（用于兼容旧版路径，不带/users前缀）
root_router = APIRouter(tags=["auth"])

# 创建用户路由（新版路径，带/users前缀）
user_router = APIRouter(prefix="/users", tags=["users"])

# 注册旧版路由（不带/users前缀）
root_router.add_api_route(
    "/login",
    users.login,
    methods=["POST"],
    response_model=users.Token,
    tags=["auth"],
    description="用户登录（旧版路径）"
)

root_router.add_api_route(
    "/register",
    users.register,
    methods=["POST"],
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["auth"],
    description="用户注册（旧版路径）"
)

root_router.add_api_route(
    "/password/reset-request",
    users.request_password_reset,
    methods=["POST"],
    response_model=users.PasswordResetResponse,
    tags=["auth"],
    description="请求重置密码（旧版路径）"
)

root_router.add_api_route(
    "/password/reset-verify",
    users.verify_password_reset,
    methods=["POST"],
    response_model=users.PasswordResetResponse,
    tags=["auth"],
    description="验证密码重置（旧版路径）"
)

# 注册新版路由（带/users前缀）
# 认证相关路由
user_router.add_api_route(
    "/login",
    users.login,
    methods=["POST"],
    response_model=users.Token,
    tags=["auth"],
    include_in_schema=False,
    description="用户登录"
)

user_router.add_api_route(
    "/register",
    users.register,
    methods=["POST"],
    response_model=UserResponse,
    status_code=status.HTTP_201_CREATED,
    tags=["auth"],
    include_in_schema=False,
    description="用户注册"
)

user_router.add_api_route(
    "/password/reset-request",
    users.request_password_reset,
    methods=["POST"],
    response_model=users.PasswordResetResponse,
    tags=["auth"],
    include_in_schema=False,
    description="请求重置密码"
)

user_router.add_api_route(
    "/password/reset-verify",
    users.verify_password_reset,
    methods=["POST"],
    response_model=users.PasswordResetResponse,
    tags=["auth"],
    include_in_schema=False,
    description="验证密码重置"
)

# 用户信息相关路由
user_router.add_api_route(
    "/me",
    users.read_user_me,
    methods=["GET"],
    response_model=UserResponse,
    tags=["users"],
    description="获取当前用户信息"
)

user_router.add_api_route(
    "/me",
    users.update_current_user,
    methods=["PUT"],
    response_model=UserResponse,
    tags=["users"],
    description="更新当前用户信息"
)

user_router.add_api_route(
    "/me/permissions",
    users.read_current_user_permissions,
    methods=["GET"],
    response_model=users.UserPermissions,
    tags=["users"],
    description="获取当前用户权限"
)

user_router.add_api_route(
    "/me/avatar",
    users.update_user_avatar,
    methods=["POST"],
    response_model=UserResponse,
    tags=["users"],
    description="更新用户头像"
)

# 用户管理路由（管理员功能）
user_router.add_api_route(
    "",
    users.read_users,
    methods=["GET"],
    response_model=list[UserResponse],
    tags=["admin"],
    description="获取用户列表（管理员）"
)

user_router.add_api_route(
    "/stats",
    users.read_user_stats,
    methods=["GET"],
    response_model=users.UserStats,
    tags=["admin"],
    description="获取用户统计信息（管理员）"
)

# 特定用户路由（注意顺序：先注册具体路径，再注册通配路径）
user_router.add_api_route(
    "/{user_id}/subscription",
    users.update_user_subscription,
    methods=["PUT"],
    response_model=UserResponse,
    tags=["admin"],
    description="更新用户订阅信息（管理员）"
)

user_router.add_api_route(
    "/{user_id}",
    users.read_user,
    methods=["GET"],
    response_model=UserResponse,
    tags=["admin"],
    description="获取指定用户信息（管理员）"
)

user_router.add_api_route(
    "/{user_id}",
    users.update_user,
    methods=["PUT"],
    response_model=UserResponse,
    tags=["admin"],
    description="更新指定用户信息（管理员）"
)

user_router.add_api_route(
    "/{user_id}",
    users.delete_user,
    methods=["DELETE"],
    status_code=status.HTTP_204_NO_CONTENT,
    tags=["admin"],
    description="删除用户（管理员）"
)

# 添加健康检查路由
@api_router.get("/healthcheck", tags=["system"])
async def healthcheck():
    """系统健康检查"""
    return {"status": "ok", "message": "系统运行正常"}

# 注册所有端点
api_router.include_router(root_router)  # 旧版路由（根路径，不带/users前缀）
api_router.include_router(user_router)  # 新版路由（带/users前缀）
api_router.include_router(backup.router, prefix="/backup", tags=["backup"])
api_router.include_router(articles.router, prefix="/articles", tags=["articles"])
api_router.include_router(bazi_analysis.router, prefix="/bazi-analysis", tags=["bazi-analysis"])

# 配置 CORS
def add_cors_middleware(app):
    """添加 CORS 中间件配置"""
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 在生产环境中应该设置具体的域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["Content-Type", "Authorization"],
        max_age=600,  # 预检请求的缓存时间（秒）
    )