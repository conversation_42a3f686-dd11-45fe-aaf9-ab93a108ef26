from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text
from src.db.base_class import Base

class Backup(Base):
    """备份记录模型"""
    __tablename__ = "backups"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), unique=True, index=True)
    status = Column(String(20))  # pending, completed, failed, deleted
    backup_type = Column(String(20))  # full, partial
    error_message = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True) 