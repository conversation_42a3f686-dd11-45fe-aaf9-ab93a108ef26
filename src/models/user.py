"""
用户数据库模型
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, JSON
from sqlalchemy.orm import relationship
from datetime import datetime, timedelta
from src.db.base_class import Base

# 获取北京时间（UTC+8）
def get_beijing_time():
    """获取北京时间（UTC+8）"""
    return datetime.utcnow() + timedelta(hours=8)

class User(Base):
    """数据库用户模型"""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(50), unique=True, nullable=False)
    phone = Column(String, unique=True, nullable=True, index=True)  # 手机号
    username = Column(String(20), nullable=False)
    hashed_password = Column(String(100), nullable=False)
    
    # 用户角色: user(普通用户), admin(管理员), superadmin(超级管理员)
    role = Column(String(10), default="user")
    
    # 账户状态
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)  # 邮箱验证状态
    
    # 权限信息
    permissions = Column(
        JSON,
        default=lambda: {
            "read": True,
            "write": False,
            "admin": False,
            "superadmin": False
        }
    )
    
    # 八字信息
    birth_year = Column(Integer, nullable=True)   # 出生年
    birth_month = Column(Integer, nullable=True)  # 出生月
    birth_day = Column(Integer, nullable=True)    # 出生日
    birth_time = Column(String, nullable=True)    # 出生时辰 (HH:mm 格式)
    gender = Column(String, nullable=True)        # 性别 (male/female)
    
    # 用户扩展信息
    avatar = Column(String, nullable=True)        # 头像URL
    bio = Column(String, nullable=True)           # 个人简介
    location = Column(String, nullable=True)      # 所在地
    website = Column(String, nullable=True)       # 个人网站
    
    # 社交媒体信息
    social_links = Column(
        JSON,
        default=lambda: {
            "wechat": None,      # 微信
            "weibo": None,       # 微博
            "qq": None,          # QQ
            "twitter": None,     # Twitter
            "facebook": None,    # Facebook
            "linkedin": None     # LinkedIn
        }
    )
    
    # 时间信息
    created_at = Column(DateTime, default=get_beijing_time)
    updated_at = Column(DateTime, default=get_beijing_time, onupdate=get_beijing_time)
    last_login = Column(DateTime, nullable=True)
    
    # 关联的八字分析
    bazi_analyses = relationship(
        "BaziAnalysis",
        back_populates="user",
        cascade="all, delete-orphan",
        lazy="select"
    )

    def __repr__(self):
        return f"<User {self.username}>"