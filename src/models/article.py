"""
文章数据库模型
定义文章表结构和关系
"""
from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, JSON, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from src.db.base_class import Base
from src.models.user import User

class Article(Base):
    """文章表"""
    __tablename__ = "articles"

    id = Column(Integer, primary_key=True, index=True)
    title = Column(String(200), nullable=False)  # 必填项
    content = Column(Text, nullable=False)       # 必填项
    slug = Column(String(200), unique=True, index=True, nullable=False)
    
    # 可选字段
    summary = Column(Text, nullable=True)         # 摘要
    cover_image = Column(String, nullable=True)   # 封面图路径
    category = Column(String, nullable=True)      # 分类
    tags = Column(JSON, default=[])                # 标签列表
    meta_title = Column(String, nullable=True)    # SEO标题
    meta_description = Column(Text, nullable=True)# SEO描述
    is_featured = Column(Boolean, default=False)  # 是否推荐
    
    # 文章状态
    status = Column(String, default="draft")  # draft, published, archived
    
    # 文章类型
    article_type = Column(String, default="general")  # general, analysis, report
    
    # 文章元数据
    article_metadata = Column(
        JSON,
        default=lambda: {
            "views": 0,
            "likes": 0,
            "shares": 0,
            "comments": 0
        }
    )
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    published_at = Column(DateTime, nullable=True)
    
    # 作者关联
    author_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"))
    author = relationship("User", backref="articles")
    
    # 文章图片关联
    images = relationship("ArticleImage", back_populates="article", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Article {self.title}>"

class ArticleImage(Base):
    """文章图片模型"""
    __tablename__ = "article_images"

    id = Column(Integer, primary_key=True, index=True)
    article_id = Column(Integer, ForeignKey("articles.id"))
    filename = Column(String)  # 文件名
    file_path = Column(String)  # 存储路径
    file_size = Column(Integer)  # 文件大小（字节）
    mime_type = Column(String)  # 文件类型
    width = Column(Integer, nullable=True)  # 图片宽度
    height = Column(Integer, nullable=True)  # 图片高度
    
    # 上传信息
    uploaded_at = Column(DateTime, default=datetime.utcnow)
    is_used = Column(Boolean, default=True)  # 是否被文章使用
    
    # 关联的文章
    article = relationship("Article", back_populates="images")

    def __repr__(self):
        return f"<ArticleImage {self.filename}>" 