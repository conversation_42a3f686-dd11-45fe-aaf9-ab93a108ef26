"""
密码重置令牌数据库模型
用于存储密码重置令牌信息
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from datetime import datetime

from ..db.database import Base

class PasswordResetToken(Base):
    """密码重置令牌表"""
    __tablename__ = "password_reset_tokens"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, index=True)
    token = Column(String, unique=True, index=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime)
    is_used = Column(Boolean, default=False)
    used_at = Column(DateTime, nullable=True)

    def __repr__(self):
        return f"<PasswordResetToken(email={self.email}, token={self.token})>" 