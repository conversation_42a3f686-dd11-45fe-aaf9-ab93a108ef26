"""
八字分析相关的数据库模型
"""
from datetime import datetime, timedelta
from sqlalchemy import Column, Integer, String, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from src.db.base_class import Base

# 获取北京时间（UTC+8）
def get_beijing_time():
    """获取北京时间（UTC+8）"""
    return datetime.utcnow() + timedelta(hours=8)

class BaziAnalysis(Base):
    """八字分析数据库模型"""
    __tablename__ = "bazi_analyses"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False)
    user_email = Column(String, index=True, nullable=False)  # 添加用户email字段
    
    # 基础信息
    birth_year = Column(Integer, nullable=False)
    birth_month = Column(Integer, nullable=False)
    birth_day = Column(Integer, nullable=False)
    birth_time = Column(String, nullable=True)
    
    # 分析结果
    analysis_result = Column(Text, nullable=True)  # 分析结果
    analysis_type = Column(String, nullable=True)  # 分析类型（前端定义）
    notes = Column(String, nullable=True)  # 备注
    
    # 时间信息
    created_at = Column(DateTime, default=get_beijing_time)
    updated_at = Column(DateTime, nullable=True)
    
    # 关联关系
    user = relationship(
        "User",
        back_populates="bazi_analyses",
        lazy="select"
    )

    def __repr__(self):
        return f"<BaziAnalysis(id={self.id}, user_email={self.user_email})>" 