"""
数据库模型初始化
注意：导入顺序很重要，需要先导入不依赖其他模型的基础模型
"""
# 1. 导入基类
from src.db.base_class import Base

# 2. 导入独立模型（不依赖其他模型的）
from .user import User

# 3. 导入依赖模型（依赖其他模型的）
from .bazi_analysis import BaziAnalysis
from .article import Article, ArticleImage

# 4. 配置映射器
from sqlalchemy.orm import configure_mappers
configure_mappers()

# 5. 导出所有模型
__all__ = [
    "Base",
    "User",
    "BaziAnalysis",
    "Article",
    "ArticleImage"
]
