"""
数据库连接和会话管理模块
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from src.config import settings
from .base_class import Base  # 使用统一定义的基类

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL,
    connect_args={"check_same_thread": False} if "sqlite" in settings.DATABASE_URL else {}
)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def init_db():
    """初始化数据库表结构"""
    try:
        # 延迟导入模型以确保元数据加载
        from src.models.user import User  # 只导入实际存在的模型
        from src.models.bazi_analysis import BaziAnalysis
        
        # 创建所有表（生产环境应使用迁移工具）
        Base.metadata.create_all(bind=engine)
        print(f"成功创建{len(Base.metadata.tables)}张数据表")
        
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        raise

def get_db():
    """依赖注入用数据库会话生成器"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()