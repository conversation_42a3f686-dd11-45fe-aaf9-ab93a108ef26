"""
用户相关的 Pydantic 模型
用于 API 请求和响应的数据验证
"""
from typing import Optional, Dict, List
from datetime import datetime
from pydantic import BaseModel, EmailStr, HttpUrl, constr, validator, ConfigDict, Field
from enum import Enum
from src.models.user import User

class Gender(str, Enum):
    """性别枚举"""
    MALE = "male"
    FEMALE = "female"

class Role(str, Enum):
    """用户角色枚举"""
    USER = "user"
    ADMIN = "admin"
    SUPERADMIN = "superadmin"

# 移除 SubscriptionType 枚举
class SubscriptionType(str, Enum):
    """订阅类型枚举"""
    NONE = "none"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"

class SocialLinks(BaseModel):
    """社交媒体链接"""
    wechat: Optional[str] = None
    weibo: Optional[str] = None
    qq: Optional[str] = None
    twitter: Optional[str] = None
    facebook: Optional[str] = None
    linkedin: Optional[str] = None

class UserBase(BaseModel):
    """用户基础信息"""
    email: EmailStr = Field(..., description="用户邮箱")
    username: str = Field(..., min_length=3, max_length=20, description="用户名")
    phone: Optional[str] = None
    
    # 用户扩展信息
    avatar: Optional[HttpUrl] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[HttpUrl] = None
    
    # 八字信息
    birth_year: Optional[int] = None
    birth_month: Optional[int] = None
    birth_day: Optional[int] = None
    birth_time: Optional[str] = None
    gender: Optional[Gender] = None

    @validator('birth_month')
    def validate_birth_month(cls, v):
        if v is not None and not 1 <= v <= 12:
            raise ValueError('月份必须在 1-12 之间')
        return v

    @validator('birth_day')
    def validate_birth_day(cls, v):
        if v is not None and not 1 <= v <= 31:
            raise ValueError('日期必须在 1-31 之间')
        return v

    @validator('birth_time')
    def validate_birth_time(cls, v):
        if v is not None:
            try:
                datetime.strptime(v, '%H:%M')
            except ValueError:
                raise ValueError('时间格式必须为 HH:mm')
        return v

class UserCreate(UserBase):
    """创建用户请求模型"""
    password: str = Field(..., min_length=6, description="密码")
    role: Optional[Role] = Role.USER
    social_links: Optional[SocialLinks] = None
    # 添加八字信息字段
    birth_year: Optional[int] = None
    birth_month: Optional[int] = None
    birth_day: Optional[int] = None
    birth_time: Optional[str] = None
    gender: Optional[Gender] = None

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.strftime("%Y-%m-%d %H:%M:%S")
        },
        json_schema_extra = {
            "example": {
                "email": "<EMAIL>",
                "password": "your_secure_password",
                "username": "username123",
                "phone": "13800138000",
                "role": "user",
                "bio": "这是我的个人简介",
                "location": "北京",
                "birth_year": 1990,
                "birth_month": 1,
                "birth_day": 1,
                "birth_time": "12:00",
                "gender": "male",
                "social_links": {
                    "wechat": "wx123456"
                }
            },
            "description": "用户注册信息。邮箱、密码和用户名为必填项，其他字段为可选。"
        }
    )

class UserUpdate(BaseModel):
    """更新用户请求模型"""
    email: Optional[EmailStr] = None
    username: Optional[str] = Field(None, min_length=3, max_length=20)
    phone: Optional[str] = None
    password: Optional[str] = Field(None, min_length=6)
    avatar: Optional[HttpUrl] = None
    bio: Optional[str] = None
    location: Optional[str] = None
    website: Optional[HttpUrl] = None
    birth_year: Optional[int] = None
    birth_month: Optional[int] = None
    birth_day: Optional[int] = None
    birth_time: Optional[str] = None
    gender: Optional[Gender] = None
    social_links: Optional[SocialLinks] = None

    class Config:
        """Pydantic配置"""
        json_schema_extra = {
            "example": {
                "username": "张三",
                "email": "<EMAIL>",
                "phone": "13800138000",
                "bio": "这是我的个人简介",
                "location": "北京",
                "birth_year": 1990,
                "birth_month": 1,
                "birth_day": 1,
                "birth_time": "12:00",
                "gender": "male"
            }
        }

class UserPermissions(BaseModel):
    """用户权限模型"""
    role: str
    permissions: Dict[str, bool]
    subscription_features: Dict[str, bool]
    is_active: bool
    is_verified: bool
    subscription_type: str
    subscription_end_date: Optional[datetime] = None

class SubscriptionUpdate(BaseModel):
    """更新订阅信息请求模型"""
    subscription_type: SubscriptionType
    subscription_end_date: Optional[datetime] = None
    subscription_features: Dict[str, bool]

class UserStats(BaseModel):
    """用户统计信息"""
    total_users: int
    active_users: int
    verified_users: int
    role_stats: Dict[str, int]

class Token(BaseModel):
    """Token响应模型"""
    access_token: str
    token_type: str

class UserOut(UserBase):
    """用户输出模型（兼容旧版备份数据）"""
    id: int = Field(..., description="用户ID")
    is_active: bool = Field(True, description="激活状态")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

    @classmethod
    def from_orm(cls, user: User):
        return cls(
            id=user.id,
            email=user.email,
            username=user.username,
            is_active=user.is_active,
            created_at=user.created_at,
            updated_at=user.updated_at,
            last_login=user.last_login
        )

class UserResponse(UserOut):
    """扩展响应模型"""
    role: str = Field("user", description="用户角色")
    updated_at: Optional[datetime] = Field(None, description="更新时间")

class UserDB(UserResponse):
    """数据库模型（用于ORM映射）"""
    hashed_password: str = Field(..., description="加密后的密码")