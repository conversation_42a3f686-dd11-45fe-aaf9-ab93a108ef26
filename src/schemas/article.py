"""
文章相关的 Pydantic 模型
用于 API 请求和响应的数据验证
"""
from typing import List, Optional, Dict
from datetime import datetime
from pydantic import BaseModel, HttpUrl, Field

class ArticleImageBase(BaseModel):
    """图片基础模型"""
    filename: str
    mime_type: str
    width: Optional[int] = None
    height: Optional[int] = None

class ArticleImageCreate(ArticleImageBase):
    """创建图片请求模型"""
    file_size: int

class ArticleImageRead(ArticleImageBase):
    """图片信息响应模型"""
    id: int
    file_path: str
    file_size: int
    uploaded_at: datetime
    is_used: bool

    class Config:
        """Pydantic配置"""
        from_attributes = True

class ArticleBase(BaseModel):
    """文章基础模型"""
    title: str = Field(
        ...,
        min_length=1,
        max_length=200,
        example="如何学习Python编程",
        description="文章标题（1-200个字符）"
    )
    content: str = Field(
        ...,
        min_length=50,
        example="Python是一门优秀的编程语言...（不少于50个字符）",
        description="文章内容（至少50个字符）"
    )
    summary: Optional[str] = None
    cover_image: Optional[str] = None
    category: Optional[str] = None
    tags: List[str] = []
    meta_title: Optional[str] = None
    meta_description: Optional[str] = None
    is_featured: bool = False
    slug: Optional[str] = Field(
        None,
        pattern=r"^[a-z0-9-]+$",
        example="learn-python",
        description="URL标识（小写字母、数字和连字符）"
    )

class ArticleCreate(ArticleBase):
    """创建文章请求模型"""
    title: str = Field(
        ...,
        min_length=1,
        max_length=200,
        example="如何学习Python编程",
        description="文章标题（5-200个字符）"
    )
    status: str = "draft"

class ArticleUpdate(ArticleBase):
    """更新文章请求模型"""
    title: Optional[str] = None
    content: Optional[str] = None
    slug: Optional[str] = None
    status: Optional[str] = None

class ArticleRead(ArticleBase):
    """文章详情响应模型"""
    id: int
    slug: str
    status: str
    author_id: int
    created_at: datetime
    updated_at: datetime
    published_at: Optional[datetime] = None
    article_metadata: Dict = Field(
        default_factory=lambda: {
            "views": 0,
            "likes": 0,
            "shares": 0,
            "comments": 0
        }
    )

class ArticleList(BaseModel):
    """文章列表响应模型"""
    total: int
    items: List[ArticleRead]
    
class ArticleStats(BaseModel):
    """文章统计信息"""
    total_articles: int
    published_articles: int
    draft_articles: int
    total_views: int
    total_likes: int
    total_comments: int 