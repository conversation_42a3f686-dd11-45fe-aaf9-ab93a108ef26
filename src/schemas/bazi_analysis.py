"""
八字分析相关的数据模型
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field

class BaziAnalysisBase(BaseModel):
    """八字分析基础模型"""
    birth_year: int = Field(
        ...,
        description="出生年份",
        example=1990,
        ge=1900,
        le=2100
    )
    birth_month: int = Field(
        ...,
        description="出生月份",
        example=1,
        ge=1,
        le=12
    )
    birth_day: int = Field(
        ...,
        description="出生日期",
        example=1,
        ge=1,
        le=31
    )
    birth_time: Optional[str] = Field(
        None,
        description="出生时间（24小时制，格式：HH:mm）",
        example="12:30",
        pattern="^([01]?[0-9]|2[0-3]):[0-5][0-9]$"
    )
    analysis_type: Optional[str] = Field(
        None,
        description="分析类型（事业/感情/健康等）",
        example="career",
        max_length=50
    )
    notes: Optional[str] = Field(
        None,
        description="备注信息",
        example="希望重点分析事业发展",
        max_length=500
    )

class BaziAnalysisCreate(BaziAnalysisBase):
    """创建八字分析的请求模型"""
    class Config:
        json_schema_extra = {
            "example": {
                "birth_year": 1990,
                "birth_month": 1,
                "birth_day": 1,
                "birth_time": "12:30",
                "analysis_type": "career",
                "notes": "希望重点分析事业发展"
            }
        }

class BaziAnalysisUpdate(BaseModel):
    """更新八字分析的请求模型"""
    analysis_type: Optional[str] = Field(
        None,
        description="分析类型",
        example="career",
        max_length=50
    )
    notes: Optional[str] = Field(
        None,
        description="备注信息",
        example="补充说明：希望着重分析2024年运势",
        max_length=500
    )

class BaziAnalysisRead(BaziAnalysisBase):
    """返回给客户端的八字分析模型"""
    id: int = Field(..., description="分析记录ID")
    analysis_result: Optional[str] = Field(
        None,
        description="分析结果",
        example="根据您的八字分析，2024年事业运势..."
    )
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="最后更新时间")

    class Config:
        from_attributes = True

class BaziAnalysisInDB(BaziAnalysisBase):
    """数据库中的八字分析模型"""
    id: int
    user_id: int
    created_at: datetime

    class Config:
        """Pydantic配置"""
        from_attributes = True

class BaziAnalysisList(BaseModel):
    """八字分析列表响应模型"""
    total: int = Field(..., description="总记录数")
    items: List[BaziAnalysisRead] = Field(..., description="分析记录列表")

    class Config:
        json_schema_extra = {
            "example": {
                "total": 1,
                "items": [{
                    "id": 1,
                    "birth_year": 1990,
                    "birth_month": 1,
                    "birth_day": 1,
                    "birth_time": "12:30",
                    "analysis_type": "career",
                    "analysis_result": "根据您的八字分析，2024年事业运势...",
                    "notes": "希望重点分析事业发展",
                    "created_at": "2024-02-20T15:42:08",
                    "updated_at": None
                }]
            }
        }

class BaziAnalysisStats(BaseModel):
    """八字分析统计信息"""
    total_analyses: int
    analyses_by_type: dict[str, int] 