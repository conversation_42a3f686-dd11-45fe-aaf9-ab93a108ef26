"""
密码重置相关的 Pydantic 模型
用于 API 请求和响应的数据验证
"""
from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional

class PasswordResetRequest(BaseModel):
    """密码重置请求模型"""
    email: EmailStr

class PasswordResetResponse(BaseModel):
    """密码重置响应模型"""
    message: str
    email: EmailStr

class PasswordResetVerify(BaseModel):
    """密码重置验证模型"""
    token: str
    new_password: str

class PasswordResetToken(BaseModel):
    """密码重置令牌模型"""
    email: EmailStr
    token: str
    created_at: datetime
    expires_at: datetime
    is_used: bool = False
    used_at: Optional[datetime] = None

    class Config:
        """Pydantic配置"""
        from_attributes = True  # 允许从ORM模型创建 