"""
数据备份相关的模型
用于数据导出和导入
"""
from typing import List, Dict, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .user import UserOut  # 确保用户模型已导入
from pydantic import EmailStr

class BackupBase(BaseModel):
    """备份基础模型"""
    description: Optional[str] = None
    backup_type: str = "full"  # full, incremental, differential

class BackupCreate(BaseModel):
    """创建备份的请求模型"""
    pass  # 不需要请求参数

class BackupResponse(BaseModel):
    """备份响应模型"""
    message: str
    timestamp: datetime

class BackupRead(BackupBase):
    """备份信息响应模型"""
    id: int
    created_at: datetime
    status: str  # pending, running, completed, failed
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    error_message: Optional[str] = None

    class Config:
        """Pydantic配置"""
        from_attributes = True

class UserBackup(BaseModel):
    """用户备份数据模型"""
    email: str
    username: str
    hashed_password: str
    role: str
    is_active: bool
    is_verified: bool
    subscription_type: str
    subscription_end_date: Optional[datetime]
    subscription_features: Dict
    permissions: Dict
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime]
    birth_year: Optional[int]
    birth_month: Optional[int]
    birth_day: Optional[int]
    birth_time: Optional[str]
    gender: Optional[str]
    phone: Optional[str]

class DatabaseBackup(BaseModel):
    """数据库备份模型"""
    version: str
    backup_date: datetime
    users: List[UserBackup]
    metadata: Dict

class BackupUser(BaseModel):
    """备份专用用户模型（允许ID缺失）"""
    email: EmailStr
    username: str
    is_active: bool = True
    role: str = "user"
    created_at: datetime
    hashed_password: str  # 必须包含加密密码
    
    class Config:
        from_attributes = True
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }

class BackupData(BaseModel):
    """备份数据模型"""
    version: str
    backup_time: datetime  # 统一字段名称
    users: List[BackupUser] = Field(default_factory=list)
    metadata: dict = Field(default_factory=dict)

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
        # 允许接收额外字段但忽略它们
        extra = "ignore" 