"""
八字分析的数据库 CRUD 操作
所有操作都基于当前登录用户的权限
"""
from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, extract
from fastapi import HTTPException, status
from src.models.bazi_analysis import BaziAnalysis
from src.models.user import User
from src.schemas.bazi_analysis import BaziAnalysisCreate, BaziAnalysisUpdate

def create_bazi_analysis(
    db: Session,
    current_user: User,
    analysis: BaziAnalysisCreate
) -> BaziAnalysis:
    """
    创建新的八字分析
    使用当前用户的信息创建
    """
    db_analysis = BaziAnalysis(
        user_id=current_user.id,
        user_email=current_user.email,
        **analysis.dict()
    )
    db.add(db_analysis)
    db.commit()
    db.refresh(db_analysis)
    return db_analysis

def get_bazi_analysis(
    db: Session, 
    analysis_id: int,
    current_user: User
) -> Optional[BaziAnalysis]:
    """
    获取指定ID的八字分析记录
    使用当前用户的email作为唯一标识进行权限验证
    """
    query = db.query(BaziAnalysis).filter(BaziAnalysis.id == analysis_id)
    
    # 如果不是管理员，只能查看自己的分析
    if current_user.role not in ["admin", "superadmin"]:
        query = query.filter(BaziAnalysis.user_id == current_user.id)
    
    return query.first()

def get_user_bazi_analyses(
    db: Session,
    current_user: User,
    skip: int = 0,
    limit: int = 100
) -> List[BaziAnalysis]:
    """
    获取八字分析列表
    使用当前用户的email作为唯一标识进行权限验证
    """
    query = db.query(BaziAnalysis)
    
    # 如果不是管理员，只能查看自己的分析
    if current_user.role not in ["admin", "superadmin"]:
        query = query.filter(BaziAnalysis.user_id == current_user.id)
    
    return query.order_by(BaziAnalysis.created_at.desc()).offset(skip).limit(limit).all()

def update_bazi_analysis(
    db: Session,
    current_user: User,
    analysis_id: int,
    analysis: BaziAnalysisUpdate
) -> BaziAnalysis:
    """
    更新八字分析
    只能更新自己的分析记录
    """
    # 获取分析记录
    db_analysis = get_bazi_analysis(db, analysis_id, current_user)
    if not db_analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析记录不存在"
        )
    
    # 检查权限
    if db_analysis.user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="无权修改他人的分析记录"
        )
    
    # 更新分析记录
    update_data = analysis.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_analysis, field, value)
    
    # 更新时间
    from src.models.bazi_analysis import get_beijing_time
    db_analysis.updated_at = get_beijing_time()
    
    db.commit()
    db.refresh(db_analysis)
    return db_analysis

def delete_bazi_analysis(
    db: Session,
    current_user: User,
    analysis_id: int
) -> None:
    """
    删除八字分析
    使用当前用户的唯一标识进行权限验证
    """
    db_analysis = get_bazi_analysis(db, analysis_id, current_user)
    if not db_analysis:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="分析记录不存在或无权访问"
        )
    
    db.delete(db_analysis)
    db.commit()

def get_user_bazi_stats(db: Session, user_id: int) -> Dict[str, Any]:
    """
    获取用户的八字分析统计信息
    
    Args:
        db: 数据库会话
        user_id: 用户ID
        
    Returns:
        Dict[str, Any]: 统计信息字典
    """
    try:
        # 总分析数
        total_analyses = db.query(BaziAnalysis).filter(
            BaziAnalysis.user_id == user_id
        ).count()
        
        # 按类型统计
        analyses_by_type = db.query(
            BaziAnalysis.analysis_type,
            func.count(BaziAnalysis.id)
        ).filter(
            BaziAnalysis.user_id == user_id
        ).group_by(BaziAnalysis.analysis_type).all()
        
        # 本月分析数
        current_month_analyses = db.query(BaziAnalysis).filter(
            BaziAnalysis.user_id == user_id,
            extract('month', BaziAnalysis.created_at) == datetime.now().month,
            extract('year', BaziAnalysis.created_at) == datetime.now().year
        ).count()
        
        return {
            "total_analyses": total_analyses,
            "analyses_by_type": dict(analyses_by_type),
            "analyses_this_month": current_month_analyses,
            "last_analysis_date": db.query(BaziAnalysis.created_at).filter(
                BaziAnalysis.user_id == user_id
            ).order_by(BaziAnalysis.created_at.desc()).first()
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取统计信息失败: {str(e)}"
        )

def get_analyses_count(db: Session, current_user: User) -> int:
    """获取分析记录总数"""
    query = db.query(func.count(BaziAnalysis.id))
    
    # 如果不是管理员，只能看到自己的分析数量
    if current_user.role not in ["admin", "superadmin"]:
        query = query.filter(BaziAnalysis.user_id == current_user.id)
    
    return query.scalar() 