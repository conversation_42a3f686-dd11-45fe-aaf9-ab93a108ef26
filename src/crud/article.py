"""
文章相关的数据库操作
包含文章和图片的 CRUD 操作
"""
from typing import List, Optional, Dict
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import desc, func, and_
from fastapi import UploadFile, HTTPException, status
import os
import aiofiles
from PIL import Image
import slugify
from sqlalchemy.exc import SQLAlchemyError
import traceback

from src.models.article import Article, ArticleImage
from src.schemas.article import ArticleCreate, ArticleUpdate, ArticleImageCreate
from src.config import settings
from src.core.logger import logger

# 文章状态常量
ARTICLE_STATUS = {
    "DRAFT": "draft",
    "PUBLISHED": "published",
    "ARCHIVED": "archived"
    # 移除PENDING状态以简化流程
}

async def save_upload_file(upload_file: UploadFile, folder: str) -> Dict:
    """保存上传的文件
    
    Args:
        upload_file: 上传的文件
        folder: 保存的文件夹
        
    Returns:
        包含文件信息的字典
        
    Raises:
        HTTPException: 文件保存失败时抛出异常
    """
    try:
        # 确保目录存在
        os.makedirs(folder, exist_ok=True)
        
        # 生成文件名
        file_name = f"{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{upload_file.filename}"
        file_path = os.path.join(folder, file_name)
        
        # 保存文件
        async with aiofiles.open(file_path, 'wb') as out_file:
            content = await upload_file.read()
            await out_file.write(content)
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        width = height = None
        
        # 如果是图片，获取尺寸信息
        if upload_file.content_type.startswith('image/'):
            with Image.open(file_path) as img:
                width, height = img.size
        
        return {
            "filename": file_name,
            "file_path": file_path,
            "file_size": file_size,
            "mime_type": upload_file.content_type,
            "width": width,
            "height": height
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件保存失败: {str(e)}"
        )

def create_article(db: Session, article: ArticleCreate, author_id: int) -> Article:
    """创建新文章"""
    try:
        # 自动生成slug逻辑优化（兼容手动输入）
        if article.slug:
            # 清理手动输入的slug
            cleaned_slug = slugify.slugify(article.slug)
            if cleaned_slug != article.slug:
                raise HTTPException(
                    status_code=400,
                    detail="URL标识包含非法字符，请使用字母、数字和连字符"
                )
            article.slug = cleaned_slug
            
            # 检查并处理冲突（即使手动输入也自动添加后缀）
            base_slug = article.slug
            counter = 1
            while db.query(Article).filter(Article.slug == article.slug).first():
                article.slug = f"{base_slug}-{counter}"
                counter += 1
        else:
            # 自动生成slug
            base_slug = slugify.slugify(article.title)
            temp_slug = base_slug
            counter = 1
            while db.query(Article).filter(Article.slug == temp_slug).first():
                temp_slug = f"{base_slug}-{counter}"
                counter += 1
            article.slug = temp_slug
        
        # 设置初始状态
        if article.status == ARTICLE_STATUS["PUBLISHED"]:
            published_at = datetime.utcnow()
        else:
            published_at = None
        
        # 创建文章对象时只传递有效字段
        db_article = Article(
            title=article.title,
            content=article.content,
            slug=article.slug,
            summary=article.summary,
            cover_image=article.cover_image,
            category=article.category,
            tags=article.tags,
            meta_title=article.meta_title,
            meta_description=article.meta_description,
            is_featured=article.is_featured,
            author_id=author_id,
            status=article.status,
            published_at=published_at
        )
        
        db.add(db_article)
        db.commit()
        db.refresh(db_article)
        return db_article
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建文章失败: {str(e)}"
        )

def get_article(db: Session, article_id: int) -> Optional[Article]:
    """获取指定文章"""
    return db.query(Article).filter(Article.id == article_id).first()

def increment_view_count(db: Session, article_id: int) -> bool:
    """增加文章浏览量
    
    Args:
        db: 数据库会话
        article_id: 文章ID
        
    Returns:
        是否成功增加浏览量
    """
    try:
        db.query(Article).filter(
            and_(
                Article.id == article_id,
                Article.status == ARTICLE_STATUS["PUBLISHED"]
            )
        ).update(
            {Article.view_count: Article.view_count + 1},
            synchronize_session=False
        )
        db.commit()
        return True
    except Exception:
        db.rollback()
        return False

def get_article_with_view_count(db: Session, article_id: int, increment_views: bool = True) -> Optional[Article]:
    """获取文章并增加浏览量
    
    Args:
        db: 数据库会话
        article_id: 文章ID
        increment_views: 是否增加浏览量，默认为True
        
    Returns:
        文章对象
    """
    article = get_article(db, article_id)
    if article and increment_views and article.status == ARTICLE_STATUS["PUBLISHED"]:
        increment_view_count(db, article_id)
    return article

def get_article_by_slug(db: Session, slug: str) -> Optional[Article]:
    """通过 slug 获取文章"""
    return db.query(Article).filter(Article.slug == slug).first()

def get_articles(
    db: Session,
    skip: int = 0,
    limit: int = 10,
    category: Optional[str] = None,
    status: Optional[str] = None,
    author_id: Optional[int] = None,
    tag: Optional[str] = None,
    search: Optional[str] = None,
    order_by: str = "created_at",
    is_admin: bool = False
) -> Dict:
    """获取文章列表"""
    try:
        query = db.query(Article)
        
        # 非管理员只能看到已发布的文章
        if not is_admin:
            query = query.filter(Article.status == ARTICLE_STATUS["PUBLISHED"])
        
        # 应用过滤条件
        if category:
            query = query.filter(Article.category == category)
        if status and is_admin and status in ARTICLE_STATUS.values():
            query = query.filter(Article.status == status)
        if author_id and is_admin:
            query = query.filter(Article.author_id == author_id)
        if tag:
            query = query.filter(Article.tags.contains([tag]))
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (Article.title.ilike(search_term)) |
                (Article.content.ilike(search_term)) |
                (Article.summary.ilike(search_term))
            )
        
        # 应用排序
        if order_by == "created_at":
            query = query.order_by(desc(Article.created_at))
        elif order_by == "updated_at":
            query = query.order_by(desc(Article.updated_at))
        elif order_by == "view_count":
            query = query.order_by(desc(Article.view_count))
        elif order_by == "like_count":
            query = query.order_by(desc(Article.like_count))
        
        # 应用分页
        total = query.count()
        articles = query.offset(skip).limit(limit).all()
        
        return {"total": total, "items": articles}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文章列表失败: {str(e)}"
        )

def update_article(db: Session, article_id: int, article_update: ArticleUpdate) -> Article:
    """更新文章"""
    try:
        db_article = get_article(db, article_id)
        if not db_article:
            raise HTTPException(status_code=404, detail="文章不存在")
        
        # 将 Pydantic 模型转换为字典，排除空值
        update_data = {
            key: value for key, value in article_update.model_dump().items()
            if value is not None
        }
        
        # 处理状态更新
        if "status" in update_data:
            new_status = update_data["status"]
            if new_status not in ARTICLE_STATUS.values():
                raise HTTPException(
                    status_code=400,
                    detail=f"无效的文章状态，可选值: {', '.join(ARTICLE_STATUS.values())}"
                )
            
            # 设置发布时间
            if new_status == ARTICLE_STATUS["PUBLISHED"]:
                update_data["published_at"] = datetime.utcnow()
            elif new_status == ARTICLE_STATUS["ARCHIVED"]:
                update_data["published_at"] = None
        
        # 更新字段
        for key, value in update_data.items():
            setattr(db_article, key, value)
        
        db.commit()
        db.refresh(db_article)
        return db_article
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新文章失败: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"更新文章失败: {str(e)}"
        )

def delete_article(db: Session, article_id: int) -> bool:
    """删除文章"""
    try:
        db_article = get_article(db, article_id)
        if not db_article:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文章不存在"
            )
        
        # 删除关联的图片文件
        for image in db_article.images:
            try:
                os.remove(image.file_path)
            except OSError:
                pass  # 忽略文件删除错误
        
        db.delete(db_article)
        db.commit()
        return True
    except HTTPException:
        db.rollback()
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除文章失败: {str(e)}"
        )

def create_article_image(
    db: Session,
    article_id: int,
    image_data: Dict
) -> Optional[ArticleImage]:
    """创建文章图片"""
    db_image = ArticleImage(
        article_id=article_id,
        **image_data
    )
    
    db.add(db_image)
    db.commit()
    db.refresh(db_image)
    return db_image

def get_article_stats(db: Session) -> Dict:
    """获取文章统计信息（兼容多数据库版）"""
    try:
        # 使用数据库无关的JSON处理方式
        total_views = 0
        total_likes = 0
        total_comments = 0
        
        # 逐条统计保证兼容性（适用于小型数据集）
        for article in db.query(Article).all():
            total_views += article.article_metadata.get("views", 0)
            total_likes += article.article_metadata.get("likes", 0)
            total_comments += article.article_metadata.get("comments", 0)
        
        return {
            "total_articles": db.query(func.count(Article.id)).scalar(),
            "published_articles": db.query(func.count(Article.id))
                                  .filter(Article.status == ARTICLE_STATUS["PUBLISHED"]).scalar(),
            "draft_articles": db.query(func.count(Article.id))
                              .filter(Article.status == ARTICLE_STATUS["DRAFT"]).scalar(),
            "total_views": total_views,
            "total_likes": total_likes,
            "total_comments": total_comments
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"获取统计信息失败: {str(e)}"
        ) 