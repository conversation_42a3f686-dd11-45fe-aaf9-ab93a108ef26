"""
用户相关的数据库操作
包含用户的 CRUD 操作和认证
"""
from typing import List, Optional, Dict
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import func
from fastapi import HTTPException, status

from src.core.security import get_password_hash, verify_password
from src.models.user import User
from src.schemas.user import UserCreate, UserUpdate, SubscriptionUpdate

def get_beijing_time() -> datetime:
    """获取北京时间（UTC+8）"""
    return datetime.utcnow() + timedelta(hours=8)

def get_user(db: Session, user_id: int) -> Optional[User]:
    """通过ID获取用户"""
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """通过邮箱获取用户"""
    return db.query(User).filter(User.email == email).first()

def get_user_by_phone(db: Session, phone: str) -> Optional[User]:
    """通过手机号获取用户"""
    return db.query(User).filter(User.phone == phone).first()

def get_users(
    db: Session,
    skip: int = 0,
    limit: int = 100,
    role: Optional[str] = None
) -> List[User]:
    """获取用户列表"""
    query = db.query(User)
    if role:
        query = query.filter(User.role == role)
    return query.offset(skip).limit(limit).all()

def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """通过用户名获取用户"""
    return db.query(User).filter(User.username == username).first()

def create_user(db: Session, user: UserCreate) -> User:
    """创建新用户"""
    # 检查邮箱是否已存在
    if get_user_by_email(db, user.email):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已被注册"
        )
    
    # 检查用户名是否已存在
    if get_user_by_username(db, user.username):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已被使用"
        )
    
    # 检查手机号是否已存在
    if user.phone and get_user_by_phone(db, user.phone):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="手机号已被注册"
        )
    
    # 创建用户对象
    db_user = User(
        email=user.email,
        username=user.username,
        phone=user.phone,
        hashed_password=get_password_hash(user.password),
        role=user.role,
        permissions={"read": True, "write": True, "admin": False, "superadmin": False},
        is_active=True,
        is_verified=False,
        avatar=user.avatar,
        bio=user.bio,
        location=user.location,
        website=user.website,
        birth_year=user.birth_year,
        birth_month=user.birth_month,
        birth_day=user.birth_day,
        birth_time=user.birth_time,
        gender=user.gender,
        social_links=user.social_links.dict() if user.social_links else {},
        created_at=get_beijing_time(),
        updated_at=get_beijing_time()
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
    """更新用户信息"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.model_dump(exclude_unset=True)
    
    # 检查邮箱是否已被其他用户使用
    if "email" in update_data and update_data["email"] != db_user.email:
        existing_user = get_user_by_email(db, update_data["email"])
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册"
            )
    
    # 检查手机号是否已被其他用户使用
    if "phone" in update_data and update_data["phone"] != db_user.phone:
        existing_user = get_user_by_phone(db, update_data["phone"])
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="手机号已被注册"
            )
    
    # 更新用户信息
    for field, value in update_data.items():
        # 特殊处理密码字段
        if field == "password":
            db_user.hashed_password = get_password_hash(value)
        # 特殊处理权限字段
        elif field == "permissions":
            # 确保基本权限不被移除
            value["read"] = True  # 保持基本读权限
            if db_user.role in ["admin", "superadmin"]:
                value["write"] = True  # 管理员保持写权限
                value["admin"] = True  # 管理员保持管理权限
            if db_user.role == "superadmin":
                value["superadmin"] = True  # 超级管理员保持超级管理权限
            db_user.permissions = value
        # 特殊处理订阅功能字段
        elif field == "subscription_features":
            # 确保基本功能不被移除
            value["basic"] = True  # 保持基本功能
            if db_user.role in ["admin", "superadmin"]:
                value["admin"] = True  # 管理员保持管理功能
            db_user.subscription_features = value
        # 特殊处理社交媒体链接
        elif field == "social_links" and value:
            db_user.social_links = value.dict()
        # 其他字段直接更新
        else:
            setattr(db_user, field, value)
    
    # 使用北京时间更新时间戳
    db_user.updated_at = get_beijing_time()
    db.commit()
    db.refresh(db_user)
    return db_user

def delete_user(db: Session, user_id: int) -> bool:
    """删除用户"""
    db_user = get_user(db, user_id)
    if not db_user:
        return False
    
    db.delete(db_user)
    db.commit()
    return True

def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    """用户认证
    
    Args:
        db: 数据库会话
        email: 用户邮箱
        password: 明文密码
        
    Returns:
        认证成功返回用户对象，失败返回 None
    """
    print(f"尝试认证用户: {email}")  # 添加日志
    user = get_user_by_email(db, email)
    if not user:
        print(f"用户不存在: {email}")  # 添加日志
        return None
    if not verify_password(password, user.hashed_password):
        print(f"密码验证失败: {email}")  # 添加日志
        return None
    
    # 更新最后登录时间（使用北京时间）
    user.last_login = get_beijing_time()
    db.commit()
    
    print(f"用户认证成功: {email}")  # 添加日志
    return user

def update_user_password(db: Session, user_id: int, new_password: str) -> Optional[User]:
    """更新用户密码"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    db_user.hashed_password = get_password_hash(new_password)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_subscription(
    db: Session,
    user_id: int,
    subscription: SubscriptionUpdate
) -> Optional[User]:
    """更新用户订阅信息"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    db_user.subscription_type = subscription.subscription_type
    db_user.subscription_end_date = subscription.subscription_end_date
    db_user.subscription_features = subscription.subscription_features
    
    db.commit()
    db.refresh(db_user)
    return db_user

def get_user_stats(db: Session) -> Dict:
    """获取用户统计信息"""
    total_users = db.query(func.count(User.id)).scalar()
    active_users = db.query(func.count(User.id)).filter(User.is_active == True).scalar()
    verified_users = db.query(func.count(User.id)).filter(User.is_verified == True).scalar()
    
    # 角色统计
    role_stats = {}
    for role in ["user", "admin", "superadmin"]:
        count = db.query(func.count(User.id)).filter(User.role == role).scalar()
        role_stats[role] = count
    
    return {
        "total_users": total_users,
        "active_users": active_users,
        "verified_users": verified_users,
        "role_stats": role_stats
    }