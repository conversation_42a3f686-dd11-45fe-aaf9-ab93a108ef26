from sqlalchemy.orm import Session
from datetime import datetime
from typing import Optional
import logging
from sqlalchemy import and_

logger = logging.getLogger(__name__)

from ..models.password_reset_token import PasswordResetToken
from ..schemas.password_reset import PasswordResetToken as PasswordResetTokenSchema

def create_reset_token(db: Session, token_data: PasswordResetTokenSchema) -> PasswordResetToken:
    """创建密码重置令牌"""
    try:
        db_token = PasswordResetToken(
            email=token_data.email,
            token=token_data.token,
            created_at=token_data.created_at,
            expires_at=token_data.expires_at,
            is_used=False
        )
        db.add(db_token)
        db.commit()
        db.refresh(db_token)
        return db_token
    except Exception as e:
        db.rollback()
        logger.error(f"创建重置令牌失败: {str(e)}")
        raise

def get_reset_token(db: Session, token: str) -> Optional[PasswordResetToken]:
    """通过令牌获取密码重置记录"""
    return db.query(PasswordResetToken).filter(
        PasswordResetToken.token == token,
        PasswordResetToken.is_used == False,
        PasswordResetToken.expires_at > datetime.utcnow()
    ).first()

def mark_token_as_used(db: Session, token: str) -> PasswordResetToken:
    """标记令牌为已使用"""
    db_token = get_reset_token(db, token)
    if db_token:
        db_token.is_used = True
        db_token.used_at = datetime.utcnow()
        db.commit()
        db.refresh(db_token)
    return db_token

def cleanup_user_tokens(db: Session, email: str) -> int:
    """清理指定用户的所有重置令牌"""
    result = db.query(PasswordResetToken).filter(
        PasswordResetToken.email == email
    ).delete()
    db.commit()
    return result

def cleanup_expired_tokens(db: Session) -> int:
    """清理过期的令牌"""
    try:
        result = db.query(PasswordResetToken).filter(
            PasswordResetToken.expires_at < datetime.utcnow()
        ).delete()
        db.commit()
        return result
    except Exception as e:
        db.rollback()
        logger.error(f"清理过期令牌失败: {str(e)}")
        raise 