"""
数据备份相关的数据库操作函数
包含数据导出和导入功能
"""
from datetime import datetime
from typing import List, Dict
from sqlalchemy.orm import Session
from src.models.user import User
from src.schemas.backup import BackupData
from src.schemas.user import UserOut
from src.crud.user import get_user_by_email
from fastapi import HTTPException
from src.config import settings
import os
import shutil
import zipfile
import logging

logger = logging.getLogger(__name__)

def export_users(db: Session) -> Dict:
    """导出用户数据"""
    users = db.query(User).all()
    user_data = []
    
    for user in users:
        user_data.append({
            "email": user.email,
            "username": user.username,
            "hashed_password": user.hashed_password,
            "role": user.role,
            "is_active": user.is_active,
            "is_verified": user.is_verified,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "birth_year": user.birth_year,
            "birth_month": user.birth_month,
            "birth_day": user.birth_day,
            "birth_time": user.birth_time,
            "gender": user.gender,
            "phone": user.phone
        })
    
    current_time = datetime.utcnow()
    return {
        "version": "1.0",
        "backup_date": current_time.isoformat(),
        "users": user_data,
        "metadata": {
            "total_users": len(users),
            "backup_type": "full",
            "timestamp": current_time.isoformat()
        }
    }

def import_users(db: Session, backup: BackupData) -> int:
    """从备份数据导入用户"""
    imported_count = 0
    
    for user_data in backup.users:
        try:
            # 检查用户是否已存在
            existing_user = get_user_by_email(db, user_data.email)
            if existing_user:
                # 更新现有用户
                for key, value in user_data.dict().items():
                    setattr(existing_user, key, value)
                db.add(existing_user)
            else:
                # 创建新用户
                new_user = User(**user_data.dict())
                db.add(new_user)
            
            imported_count += 1
            
        except Exception as e:
            logger.error(f"导入用户 {user_data.email} 失败: {str(e)}")
            continue
    
    db.commit()
    return imported_count

def create_backup_file() -> str:
    """创建数据库备份文件"""
    # 获取数据库路径
    db_path = settings.DATABASE_URL.replace("sqlite:///", "")
    
    # 创建备份目录
    backup_dir = os.path.join(settings.BASE_DIR, "backups")
    os.makedirs(backup_dir, exist_ok=True)
    
    # 生成带时间戳的备份文件名
    timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
    backup_name = f"db_backup_{timestamp}.db"
    backup_path = os.path.join(backup_dir, backup_name)
    
    # 自动创建恢复前的备份
    shutil.copy2(db_path, backup_path)
    
    # 异常时自动回滚
    if os.path.exists(backup_path):
        shutil.copy2(backup_path, db_path)
    
    return backup_path