"""
项目配置模块
使用 pydantic 进行配置验证和管理
环境变量优先级：系统环境变量 > .env 文件 > 默认值
"""
from pydantic_settings import BaseSettings
from typing import List, Union
from pydantic import AnyHttpUrl, Field, validator
import os
import logging

logger = logging.getLogger(__name__)

class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = Field(default='北斗九号用户管理系统', description='项目名称')
    API_V1_STR: str = Field(default='/api/v1', description='API版本路径')
    ENVIRONMENT: str = Field(default='dev', description='运行环境 (dev/test/prod)')
    FRONTEND_URL: str = Field(default='https://www.9day.tech/', description='前端URL')
    BASE_DIR: str = Field(
        default=os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
        description='项目根目录'
    )
    
    # 数据库配置（支持多数据库）
    DATABASE_URL: str = Field(
        default='sqlite:///./data/sql_app.db',
        description='数据库连接URL'
    )
    DB_POOL_SIZE: int = Field(default=5, description='数据库连接池大小')
    DB_MAX_OVERFLOW: int = Field(default=10, description='连接池最大溢出数量')
    
    # 安全配置
    SECRET_KEY: str = Field(
        ..., 
        description='JWT密钥（至少32字符）',
        min_length=32
    )
    ALGORITHM: str = Field(
        default='HS256', 
        description='JWT加密算法',
        pattern='^(HS256|HS384|HS512|RS256)$'
    )
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=60 * 24 * 30,  # 30天
        description='访问令牌过期时间（分钟）',
        gt=0
    )
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = Field(
        default=24,
        description='密码重置令牌过期时间（小时）'
    )
    
    # SMTP 邮件配置
    SMTP_HOST: str = Field(default='smtp.gmail.com', description='SMTP服务器地址')
    SMTP_PORT: int = Field(default=587, description='SMTP服务器端口')
    SMTP_USER: str = Field(default='<EMAIL>', description='SMTP用户名')
    SMTP_PASSWORD: str = Field(..., description='SMTP密码')
    
    # 邮件服务配置
    MAIL_USERNAME: str = Field(default='<EMAIL>', description='邮件用户名')
    MAIL_PASSWORD: str = Field(..., description='邮件密码')
    MAIL_FROM: str = Field(default='<EMAIL>', description='发件人地址')
    MAIL_PORT: int = Field(default=587, description='邮件服务器端口')
    MAIL_SERVER: str = Field(default='smtp.gmail.com', description='邮件服务器地址')
    MAIL_SSL_TLS: bool = Field(default=True, description='是否使用SSL/TLS')
    
    # CORS 配置
    BACKEND_CORS_ORIGINS: Union[str, List[str]] = Field(
        default="*",
        description='允许的跨域源'
    )

    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v):
        """处理 CORS 配置格式"""
        if isinstance(v, str):
            return [item.strip() for item in v.split(",")] if v != "*" else ["*"]
        return v

    # 文件存储配置
    UPLOAD_DIR: str = Field(
        default='uploads',
        description='文件上传存储目录（相对项目根目录）'
    )
    MAX_UPLOAD_SIZE: int = Field(
        default=10 * 1024 * 1024,  # 10MB
        description='最大上传文件大小（字节）'
    )
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=['image/jpeg', 'image/png', 'application/pdf'],
        description='允许上传的文件MIME类型'
    )
    
    # 备份配置
    BACKUP_DIR: str = Field(
        default='backups',
        description='数据库备份存储目录'
    )
    MAX_BACKUP_FILES: int = Field(
        default=5,
        description='最大保留备份文件数量'
    )
    
    # 超级管理员配置
    FIRST_SUPERUSER: str = Field(
        ...,
        description='初始超级管理员邮箱',
        pattern=r'^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$'
    )
    FIRST_SUPERUSER_PASSWORD: str = Field(
        ...,
        description='初始超级管理员密码',
        min_length=8
    )

    class Config:
        env_file = ".env"
        case_sensitive = True
        env_file_encoding = "utf-8"

    @property
    def absolute_upload_dir(self) -> str:
        """获取绝对上传目录路径"""
        path = os.path.join(self.BASE_DIR, self.UPLOAD_DIR)
        os.makedirs(path, exist_ok=True)
        return path

    @property
    def absolute_backup_dir(self) -> str:
        """获取绝对备份目录路径"""
        path = os.path.join(self.BASE_DIR, self.BACKUP_DIR)
        os.makedirs(path, exist_ok=True)
        return path

    def get_database_config(self) -> dict:
        """获取数据库连接配置字典"""
        return {
            'url': self.DATABASE_URL,
            'pool_size': self.DB_POOL_SIZE,
            'max_overflow': self.DB_MAX_OVERFLOW,
            'echo': self.ENVIRONMENT == 'dev'
        }

settings = Settings() 