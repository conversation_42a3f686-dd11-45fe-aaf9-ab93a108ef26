# 构建阶段优化
FROM python:3.11-slim AS builder

# 设置工作目录
WORKDIR /app

# 优化构建环境变量
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1

# 精简系统依赖安装
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 分阶段安装依赖（先安装基础依赖）
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 运行阶段优化
FROM python:3.11-slim

# 设置容器工作目录
WORKDIR /app

# 安全配置
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && mkdir -p /app/uploads /app/data /app/backups /app/logs \
    && chown -R appuser:appuser /app \
    && chmod 755 /app

# 仅安装运行时必要依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq5 \
    curl \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制依赖（优化路径）
COPY --from=builder /usr/local/lib/python3.11/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY --from=builder /usr/local/bin/ /usr/local/bin/

# 复制应用代码并设置权限（关键修复点）
COPY --chown=appuser:appuser . .

# 显式设置入口脚本权限（修复权限问题）
RUN chmod 755 docker-entrypoint.sh && \
    chown appuser:appuser docker-entrypoint.sh

# 声明数据卷
VOLUME ["/app/data", "/app/backups", "/app/logs"]

# 安全配置
USER appuser
EXPOSE 4000

# 健康检查优化
HEALTHCHECK --interval=30s --timeout=5s \
    CMD curl -f http://localhost:4000/api/v1/healthcheck || exit 1

# 入口点配置
ENTRYPOINT ["/app/docker-entrypoint.sh"] 