version: '3.8'

services:
  app:
    build: .
    container_name: fastapi_app
    ports:
      - "4000:4000"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
    environment:
      PROJECT_NAME: "用户管理系统"
      API_V1_STR: "/api/v1"
      TZ: "Asia/Shanghai"
      DATABASE_URL: "sqlite:///./data/sql_app.db"
      SECRET_KEY: "your-secret-key-here"
      ALGORITHM: "HS256"
      ACCESS_TOKEN_EXPIRE_MINUTES: "30"
      FIRST_SUPERUSER: "<EMAIL>"
      FIRST_SUPERUSER_PASSWORD: "admin123"
      BACKEND_CORS_ORIGINS: '["*"]'
      FRONTEND_URL: "https://www.9day.tech/"
      SMTP_HOST: "smtp.gmail.com"
      SMTP_PORT: "587"
      SMTP_USER: "<EMAIL>"
      SMTP_PASSWORD: "ftnfdecuyrnxxhll"
      PASSWORD_RESET_TOKEN_EXPIRE_HOURS: "24"
      MAIL_USERNAME: "<EMAIL>"
      MAIL_PASSWORD: "ftnfdecuyrnxxhll"
      MAIL_FROM: "<EMAIL>"
      MAIL_PORT: "587"
      MAIL_SERVER: "smtp.gmail.com"
      MAIL_SSL_TLS: "true"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/docs"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    restart: unless-stopped