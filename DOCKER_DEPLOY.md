# Docker 部署说明

## 环境要求
- Docker
- Docker Compose

## 部署步骤

### 1. 首次部署

1. 在宝塔面板中安装 Docker 管理器插件

2. 上传项目文件到服务器
```bash
# 创建项目目录
mkdir -p /www/wwwroot/fastapi_app
# 上传所有项目文件到该目录
```

3. 进入项目目录
```bash
cd /www/wwwroot/fastapi_app
```

4. 构建并启动容器
```bash
# 构建镜像并启动容器
docker-compose up -d --build
```

5. 查看日志确认部署状态
```bash
docker-compose logs -f
```

### 2. 更新部署

1. 上传新的代码到服务器

2. 重新构建并启动容器
```bash
# 停止并删除旧容器
docker-compose down

# 重新构建并启动
docker-compose up -d --build
```

### 3. 数据备份

SQLite 数据库文件位于项目根目录的 `sql_app.db`，已通过 Docker volume 映射到容器内。
建议定期备份该文件：
```bash
cp sql_app.db sql_app.db.backup
```

### 4. 注意事项

- 确保 8000 端口未被占用
- 数据库文件 `sql_app.db` 会自动创建并持久化
- 日志文件保存在 `logs` 目录下
- 如需修改端口，请同时修改 `docker-compose.yml` 中的端口映射
- 时区已设置为 Asia/Shanghai

### 5. 常用命令

```bash
# 查看容器状态
docker-compose ps

# 查看容器日志
docker-compose logs -f

# 重启容器
docker-compose restart

# 停止并删除容器
docker-compose down

# 启动容器
docker-compose up -d
``` 