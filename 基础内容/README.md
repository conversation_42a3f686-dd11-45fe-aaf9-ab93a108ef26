# 用户管理系统 (9day.user)

这是一个基于 FastAPI 框架开发的用户管理系统，使用 SQLite 作为数据库，支持用户认证、授权等基本功能。

## 项目特点

- 基于 FastAPI 框架，提供高性能的异步 API
- 使用 SQLite 数据库，易于部署和维护
- 支持数据库迁移（使用 Alembic）
- 完整的用户认证和授权系统
- 支持 JWT token 认证
- 清晰的项目结构和代码组织

## 项目结构

```
.
├── src/                    # 源代码目录
│   ├── api/               # API 路由
│   ├── core/              # 核心配置
│   ├── crud/              # 数据库操作
│   ├── db/                # 数据库配置
│   ├── models/            # 数据库模型
│   └── schemas/           # Pydantic 模型
├── tests/                 # 测试文件
├── main.py               # 应用入口
├── requirements.txt      # 项目依赖
└── start.sh             # 启动脚本
```

## 技术栈

- FastAPI: 现代化的 Python Web 框架
- SQLAlchemy: ORM 框架
- Pydantic: 数据验证
- JWT: 用户认证
- SQLite: 数据库
- Alembic: 数据库迁移工具

## 环境要求

- Python 3.7+
- 依赖包（见 requirements.txt）

## 安装和运行

1. 克隆项目并安装依赖：
```bash
pip install -r requirements.txt
```

2. 设置环境变量（可选）：
创建 `.env` 文件并配置必要的环境变量。

3. 运行项目：
```bash
./start.sh
```
或者直接运行：
```bash
uvicorn main:app --reload
```

## API 文档

启动服务后，可以访问以下地址查看 API 文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## 主要功能

- 用户注册和登录
- JWT token 认证
- 用户信息管理
- 权限控制

## 配置说明

主要配置项在 `src/core/config.py` 中：
- `PROJECT_NAME`: 项目名称
- `API_V1_STR`: API 版本前缀
- `DATABASE_URL`: 数据库连接 URL
- `SECRET_KEY`: JWT 密钥
- `ACCESS_TOKEN_EXPIRE_MINUTES`: Token 过期时间

## 安全说明

- 密码使用 bcrypt 加密存储
- 使用 JWT token 进行身份验证
- 支持 CORS 配置

## 开发说明

1. 代码风格遵循 PEP 8 规范
2. 使用 Type Hints 进行类型注解
3. 模块化的项目结构，便于扩展

## 测试

运行测试：
```bash
./run_test.sh
```