以下是整合后的 **用户管理系统开发文档**，内容经过优化和结构化，以便更好地呈现系统设计和开发的各个方面：

---

# **用户管理系统开发文档**

## **1. 项目概述**

### **1.1 项目目标**
本项目旨在开发一个功能全面的用户管理系统，具备以下核心功能：
1. 用户注册与登录。
2. 用户的增删查改（CRUD）操作。
3. 基本权限管理，区分普通用户与管理员。
4. 支持按月、按季、按年订阅功能。
5. 支付功能集成，用于管理订阅状态。
6. 内容管理，区分免费内容和付费内容。

系统初期开发基础功能，后续逐步扩展订阅、支付和内容管理功能，确保系统的可维护性和可扩展性。

---

## **2. 技术栈**

### **2.1 后端**
- **框架**：FastAPI
- **数据库**：SQLite（开发阶段），支持后期切换到 PostgreSQL 或 MySQL
- **ORM**：SQLAlchemy
- **密码加密**：Passlib
- **权限管理**：JWT（基于 `python-jose`）
- **支付集成**：Stripe（后期扩展）

### **2.2 前端**
- **框架**：Vue.js
- **状态管理**：Pinia 或 Vuex
- **UI 框架**：Element Plus 或 Ant Design Vue

---

## **3. 系统架构设计**

### **3.1 数据库设计**

#### **用户表（users）**
存储用户的基本信息。

| 字段名                 | 类型          | 描述                          |
|------------------------|---------------|-------------------------------|
| `id`                  | INTEGER       | 主键，自增                    |
| `username`            | TEXT          | 用户名，唯一                  |
| `email`               | TEXT          | 邮箱，唯一                    |
| `password`            | TEXT          | 加密后的密码                  |
| `role`                | TEXT          | 用户角色（`user` 或 `admin`） |
| `subscription_type`   | TEXT          | 订阅类型（如 `month`）        |
| `subscription_end_date` | TIMESTAMP   | 订阅到期时间                  |
| `created_at`          | TIMESTAMP     | 创建时间，默认当前时间        |
| `updated_at`          | TIMESTAMP     | 更新时间                      |

#### **内容表（content）**
存储系统中的内容信息。

| 字段名       | 类型      | 描述                     |
|--------------|-----------|--------------------------|
| `id`         | INTEGER   | 主键，自增               |
| `title`      | TEXT      | 内容标题                 |
| `body`       | TEXT      | 内容正文                 |
| `is_premium` | BOOLEAN   | 是否为付费内容           |
| `created_at` | TIMESTAMP | 创建时间，默认当前时间   |

---

### **3.2 模块划分**

#### **用户模块**
- 用户注册与登录。
- 用户信息的增删查改（CRUD）。
- 权限管理（基于角色和订阅状态）。

#### **订阅模块**
- 订阅管理：设置订阅类型和到期时间。
- 检查订阅状态。

#### **支付模块**
- 支付集成（Stripe 或 PayPal）。
- 支付成功后的回调处理。

#### **内容模块**
- 免费和付费内容的区分。
- 根据用户权限返回不同的内容。

---

## **4. 核心功能开发**

### **4.1 用户模块**

#### **功能描述**
1. **用户注册**：用户通过用户名、邮箱和密码注册。
2. **用户登录**：用户通过邮箱和密码登录，返回 JWT 令牌。
3. **用户信息管理**：
   - **增**：管理员可以新增用户。
   - **删**：管理员可以删除用户。
   - **查**：管理员可以查看用户列表，支持筛选和分页。
   - **改**：管理员可以修改用户的基本信息（如用户名、角色、订阅状态等）。
4. **权限管理**：根据用户角色和订阅状态控制访问权限。

#### **接口设计**

| 接口名称       | 方法 | 路径            | 描述                       |
|----------------|------|-----------------|----------------------------|
| 用户注册       | POST | `/register/`    | 创建新用户（普通用户注册） |
| 用户登录       | POST | `/login/`       | 用户登录，返回 JWT 令牌    |
| 获取用户列表   | GET  | `/users/`       | 管理员获取用户列表         |
| 获取用户详情   | GET  | `/users/{id}/`  | 管理员获取指定用户详情     |
| 新增用户       | POST | `/users/`       | 管理员新增用户             |
| 修改用户信息   | PUT  | `/users/{id}/`  | 管理员修改用户信息         |
| 删除用户       | DELETE | `/users/{id}/` | 管理员删除用户             |

#### **接口实现**

##### **用户注册**
```python
@app.post("/register/")
def register(username: str, email: str, password: str, db: Session = Depends(get_db)):
    hashed_password = get_password_hash(password)
    user = User(username=username, email=email, password=hashed_password, role="user")
    db.add(user)
    try:
        db.commit()
        db.refresh(user)
    except:
        db.rollback()
        raise HTTPException(status_code=400, detail="Username or email already exists")
    return {"msg": "User registered successfully"}
```

##### **用户登录**
```python
@app.post("/login/")
def login(email: str, password: str, db: Session = Depends(get_db)):
    user = db.query(User).filter(User.email == email).first()
    if not user or not verify_password(password, user.password):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    access_token = create_access_token(data={"sub": user.email, "role": user.role})
    return {"access_token": access_token, "token_type": "bearer"}
```

##### **获取用户列表**
```python
@app.get("/users/")
def get_users(skip: int = 0, limit: int = 10, db: Session = Depends(get_db), current_user: User = Depends(get_current_admin)):
    users = db.query(User).offset(skip).limit(limit).all()
    return users
```

##### **修改用户信息**
```python
@app.put("/users/{id}/")
def update_user(id: int, user_update: UserUpdate, db: Session = Depends(get_db), current_user: User = Depends(get_current_admin)):
    user = db.query(User).filter(User.id == id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    for key, value in user_update.dict(exclude_unset=True).items():
        setattr(user, key, value)
    db.commit()
    db.refresh(user)
    return user
```

---

### **4.2 订阅模块**

#### **功能描述**
1. 用户订阅管理：记录用户的订阅类型和到期时间。
2. 权限校验：根据订阅状态控制用户访问付费内容。

#### **接口设计**

| 接口名称       | 方法 | 路径              | 描述                       |
|----------------|------|-------------------|----------------------------|
| 设置订阅       | POST | `/subscribe/`     | 设置用户订阅               |
| 检查订阅状态   | GET  | `/subscription/`  | 检查用户的订阅状态         |

##### **设置订阅**
```python
@app.post("/subscribe/")
def subscribe(subscription_type: str, db: Session = Depends(get_db), user: User = Depends(get_current_user)):
    duration = {"month": 30, "quarter": 90, "year": 365}
    if subscription_type not in duration:
        raise HTTPException(status_code=400, detail="Invalid subscription type")
    user.subscription_type = subscription_type
    user.subscription_end_date = datetime.utcnow() + timedelta(days=duration[subscription_type])
    db.commit()
    return {"msg": f"Subscribed to {subscription_type} plan successfully"}
```

---

## **5. 开发计划**

### **阶段 1：核心功能**
1. 用户注册与登录。
2. 用户信息的增删查改（CRUD）。
3. 基本权限管理。

### **阶段 2：订阅功能**
1. 添加订阅字段。
2. 实现订阅管理逻辑。

### **阶段 3：支付功能**
1. 集成支付网关。
2. 实现支付回调逻辑。

### **阶段 4：内容管理**
1. 内容表设计。
2. 根据权限返回内容。

---

## **6. 总结**

通过模块化设计，本系统能够快速实现核心功能，并为后续扩展订阅、支付和内容管理功能提供基础支持。系统的每个模块独立开发，便于测试和维护，同时确保未来的可扩展性和灵活性。