# 用户管理系统 API 文档

## 基本信息

- 基础URL: `http://localhost:8000/api/v1`
- 认证方式: Bearer Token
- 内容类型: application/json

所有需要认证的接口都需要在请求头中添加 Token：
```
Authorization: Bearer your_token_here
```

## API 接口列表

### 1. 认证相关接口

#### 1.1 用户注册
- 路径: `/register`
- 方法: POST
- 权限: 无需认证

请求体:
```json
{
    "email": "<EMAIL>",
    "username": "username",
    "password": "password123"
}
```

响应示例:
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "role": "user",
    "is_active": true,
    "is_verified": false,
    "subscription_type": "none",
    "subscription_end_date": null,
    "subscription_features": {
        "basic": true,
        "premium": false,
        "admin": false
    },
    "permissions": {
        "read": true,
        "write": false,
        "admin": false,
        "superadmin": false
    },
    "created_at": "2024-01-21T12:00:00",
    "updated_at": "2024-01-21T12:00:00",
    "last_login": null
}
```

#### 1.2 用户登录
- 路径: `/login`
- 方法: POST
- 权限: 无需认证
- 说明: 使用 form-data 格式提交

请求参数:
```
username: <EMAIL>  // 使用邮箱作为用户名
password: password123
```

响应示例:
```json
{
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1...",
    "token_type": "bearer"
}
```

### 2. 用户信息接口

#### 2.1 获取当前用户信息
- 路径: `/users/me`
- 方法: GET
- 权限: 需要认证

响应示例:
```json
{
    "id": 1,
    "email": "<EMAIL>",
    "username": "username",
    "role": "user",
    "is_active": true,
    "is_verified": false,
    "subscription_type": "none",
    "subscription_end_date": null,
    "subscription_features": {
        "basic": true,
        "premium": false,
        "admin": false
    },
    "permissions": {
        "read": true,
        "write": false,
        "admin": false,
        "superadmin": false
    },
    "created_at": "2024-01-21T12:00:00",
    "updated_at": "2024-01-21T12:00:00",
    "last_login": "2024-01-21T13:00:00"
}
```

#### 2.2 获取当前用户权限
- 路径: `/users/me/permissions`
- 方法: GET
- 权限: 需要认证

响应示例:
```json
{
    "role": "user",
    "permissions": {
        "read": true,
        "write": false,
        "admin": false,
        "superadmin": false
    },
    "subscription_features": {
        "basic": true,
        "premium": false,
        "admin": false
    },
    "is_active": true,
    "is_verified": false,
    "subscription_type": "none",
    "subscription_end_date": null
}
```

#### 2.3 更新当前用户信息
- 路径: `/users/me`
- 方法: PUT
- 权限: 需要认证

请求体:
```json
{
    "email": "<EMAIL>",    // 可选
    "username": "newusername",          // 可选
    "password": "newpassword123"        // 可选
}
```

响应格式同 2.1

### 3. 管理员接口

#### 3.1 获取用户列表
- 路径: `/users`
- 方法: GET
- 权限: 需要管理员权限

查询参数:
- skip: 跳过的记录数（默认0）
- limit: 返回的最大记录数（默认100）
- role: 按角色筛选（可选）

响应示例:
```json
[
    {
        "id": 1,
        "email": "<EMAIL>",
        "username": "user1",
        // ... 其他字段同用户信息
    },
    {
        "id": 2,
        "email": "<EMAIL>",
        "username": "user2",
        // ... 其他字段同用户信息
    }
]
```

#### 3.2 获取用户统计信息
- 路径: `/users/stats`
- 方法: GET
- 权限: 需要管理员权限

响应示例:
```json
{
    "total_users": 100,
    "active_users": 80,
    "subscription_stats": {
        "none": 50,
        "monthly": 30,
        "quarterly": 15,
        "yearly": 5
    },
    "registration_trend": {
        "2024-01": 20,
        "2024-02": 25,
        "2024-03": 30
    }
}
```

#### 3.3 获取指定用户信息
- 路径: `/users/{user_id}`
- 方法: GET
- 权限: 需要管理员权限

响应格式同 2.1

#### 3.4 更新指定用户信息
- 路径: `/users/{user_id}`
- 方法: PUT
- 权限: 需要管理员权限

请求体:
```json
{
    "email": "<EMAIL>",          // 可选
    "username": "newusername",                // 可选
    "password": "newpassword123",             // 可选
    "role": "admin",                          // 可选，需要超级管理员权限
    "is_active": true,                        // 可选
    "is_verified": true,                      // 可选
    "subscription_type": "monthly",           // 可选
    "subscription_end_date": "2024-12-31T23:59:59Z",  // 可选
    "subscription_features": {                // 可选
        "basic": true,
        "premium": true
    },
    "permissions": {                          // 可选
        "read": true,
        "write": true
    }
}
```

响应格式同 2.1

#### 3.5 更新用户订阅信息
- 路径: `/users/{user_id}/subscription`
- 方法: PUT
- 权限: 需要管理员权限

请求体:
```json
{
    "subscription_type": "monthly",
    "subscription_end_date": "2024-12-31T23:59:59Z",
    "subscription_features": {
        "basic": true,
        "premium": true,
        "admin": false
    }
}
```

响应格式同 2.1

#### 3.6 删除用户
- 路径: `/users/{user_id}`
- 方法: DELETE
- 权限: 需要管理员权限（删除管理员需要超级管理员权限）

响应: 204 No Content

### 4. 数据备份接口（仅超级管理员）

#### 4.1 导出数据库
- 路径: `/backup/export`
- 方法: GET
- 权限: 需要超级管理员权限

响应示例:
```json
{
    "version": "1.0",
    "backup_date": "2024-01-21T12:00:00Z",
    "users": [
        {
            "email": "<EMAIL>",
            "username": "username",
            "hashed_password": "hashed_password_string",
            // ... 其他用户字段
        }
    ],
    "metadata": {}
}
```

#### 4.2 导入数据库
- 路径: `/backup/import`
- 方法: POST
- 权限: 需要超级管理员权限
- 内容类型: multipart/form-data

请求参数:
- backup_file: 备份文件（JSON格式）

响应示例:
```json
{
    "message": "成功导入 10 个用户",
    "import_date": "2024-01-21T12:00:00Z"
}
```

### 5. 密码重置接口

#### 5.1 请求密码重置
- 路径: `/password/reset-request`
- 方法: POST
- 权限: 无需认证

请求体:
```json
{
    "email": "<EMAIL>"
}
```

响应示例:
```json
{
    "message": "密码重置邮件已发送",
    "email": "<EMAIL>"
}
```

#### 5.2 验证重置令牌并更新密码
- 路径: `/password/reset-verify`
- 方法: POST
- 权限: 无需认证

请求体:
```json
{
    "token": "reset_token_from_email",
    "new_password": "new_password123"
}
```

响应示例:
```json
{
    "message": "密码重置成功",
    "email": "<EMAIL>"
}
```

可能的错误响应：
- 400: 无效的重置令牌
- 400: 此重置令牌已被使用
- 400: 重置令牌已过期
- 404: 用户不存在
- 500: 发送邮件失败

## 错误响应

所有接口在发生错误时都会返回统一格式的错误响应：

```json
{
    "detail": "错误信息描述"
}
```

常见HTTP状态码：
- 400: 请求参数错误
- 401: 未认证或Token过期
- 403: 权限不足
- 404: 资源不存在
- 422: 数据验证错误
- 500: 服务器内部错误

## 前端开发建议

1. 权限控制：
```typescript
// 检查用户权限
async function checkPermissions() {
    const response = await axios.get('/api/v1/users/me/permissions');
    const permissions = response.data;
    
    // 根据权限显示/隐藏功能
    if (permissions.role === 'admin') {
        showAdminFeatures();
    }
    
    // 检查订阅功能
    if (permissions.subscription_features.premium) {
        showPremiumFeatures();
    }
}
```

2. 错误处理：
```typescript
// 统一错误处理
axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            // Token过期，跳转到登录页
            router.push('/login');
        } else if (error.response?.data?.detail) {
            // 显示错误信息
            showError(error.response.data.detail);
        }
        return Promise.reject(error);
    }
);
```

3. 文件上传：
```typescript
// 导入数据库备份
async function importDatabase(file: File) {
    const formData = new FormData();
    formData.append('backup_file', file);
    
    try {
        const response = await axios.post('/api/v1/backup/import', formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        showSuccess(response.data.message);
    } catch (error) {
        handleError(error);
    }
}
```

4. 数据导出：
```typescript
// 导出数据库
async function exportDatabase() {
    try {
        const response = await axios.get('/api/v1/backup/export');
        const blob = new Blob([JSON.stringify(response.data, null, 2)], {
            type: 'application/json'
        });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `backup-${new Date().toISOString()}.json`;
        a.click();
        URL.revokeObjectURL(url);
    } catch (error) {
        handleError(error);
    }
}
```

5. 密码重置功能：
```typescript
// 请求密码重置
async function requestPasswordReset(email: string) {
    try {
        const response = await axios.post('/api/v1/password/reset-request', {
            email: email
        });
        showSuccess(response.data.message);
    } catch (error) {
        handleError(error);
    }
}

// 验证重置令牌并更新密码
async function verifyPasswordReset(token: string, newPassword: string) {
    try {
        const response = await axios.post('/api/v1/password/reset-verify', {
            token: token,
            new_password: newPassword
        });
        showSuccess(response.data.message);
        // 重置成功后跳转到登录页
        router.push('/login');
    } catch (error) {
        handleError(error);
    }
}
``` 