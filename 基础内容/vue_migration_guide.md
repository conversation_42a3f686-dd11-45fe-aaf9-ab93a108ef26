# FastAPI 用户管理系统接口文档

## 目录
1. [系统概述](#系统概述)
2. [认证接口](#认证接口)
3. [用户管理接口](#用户管理接口)
4. [数据导出导入接口](#数据导出导入接口)
5. [统计分析接口](#统计分析接口)
6. [功能与权限接口](#功能与权限接口)
7. [系统配置](#系统配置)

## 系统概述

### 基础信息
- 基础URL: `http://localhost:8000/api/v1`
- 认证方式: Bearer Token
- 响应格式: JSON
- 错误处理: HTTP状态码 + 详细错误信息
- 数据库: SQLite（可扩展到其他数据库）
- ORM: SQLAlchemy
- 迁移工具: Alembic

### 通用错误码
- 400: 请求参数错误
- 401: 未认证或Token过期
- 403: 权限不足
- 404: 资源不存在
- 422: 数据验证错误
- 500: 服务器内部错误

### 系统特性
1. 用户管理
   - 用户注册与登录
   - 角色管理（普通用户、管理员、VIP用户、高级用户）
   - 订阅管理（未订阅、月度、季度、年度、终身）
   - 权限控制
   - 功能特权

2. 数据安全
   - JWT Token认证
   - 密码加密存储
   - 数据备份与恢复
   - 用户数据导出导入

3. 性能优化
   - 数据库索引
   - 缓存支持
   - 分页查询
   - 异步处理

## 认证接口

### 1. 用户登录
#### 接口信息
- 路径: `/login`
- 方法: POST
- 权限: 无需认证

#### 请求参数
```json
{
  "username": "<EMAIL>",  // 用户邮箱
  "password": "password123"        // 用户密码
}
```

#### 响应数据
```json
{
  "access_token": "eyJ0eXAi...",  // JWT Token
  "token_type": "bearer"          // Token类型
}
```

#### Vue实现示例
```typescript
// api/auth.ts
export const login = async (email: string, password: string) => {
  try {
    const response = await axios.post('/api/v1/login', {
      username: email,
      password: password
    });
    const { access_token } = response.data;
    localStorage.setItem('token', access_token);
    return true;
  } catch (error) {
    handleError(error);  // 错误处理
    return false;
  }
}

// 使用示例
const handleLogin = async () => {
  if (await login(email.value, password.value)) {
    router.push('/dashboard');
  }
}
```

### 2. 用户注册
#### 接口信息
- 路径: `/register`
- 方法: POST
- 权限: 无需认证

#### 请求参数
```json
{
  "email": "<EMAIL>",    // 邮箱（唯一）
  "username": "username",         // 用户名（唯一）
  "password": "password123"       // 密码
}
```

#### 响应数据
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "role": "user",
  "is_active": true,
  "created_at": "2024-01-21T12:00:00Z"
}
```

#### Vue实现示例
```typescript
// api/auth.ts
export const register = async (userData: {
  email: string;
  username: string;
  password: string;
}) => {
  try {
    const response = await axios.post('/api/v1/register', userData);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      // 处理用户名或邮箱已存在的情况
      throw new Error(error.response.data.detail);
    }
    throw error;
  }
}

// 使用示例
const handleRegister = async () => {
  try {
    await register({
      email: email.value,
      username: username.value,
      password: password.value
    });
    // 注册成功，跳转到登录页
    router.push('/login');
  } catch (error) {
    // 显示错误信息
    showError(error.message);
  }
}
```

### 3. 修改密码
#### 接口信息
- 路径: `/users/me/password`
- 方法: PUT
- 权限: 需要认证

#### 请求参数
```json
{
  "current_password": "oldpassword123",
  "new_password": "newpassword123"
}
```

#### 响应数据
```json
{
  "detail": "密码修改成功"
}
```

#### Vue实现示例
```typescript
// api/user.ts
export const changePassword = async (currentPassword: string, newPassword: string) => {
  try {
    const response = await axios.put('/api/v1/users/me/password', {
      current_password: currentPassword,
      new_password: newPassword
    });
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      throw new Error('当前密码错误');
    }
    throw error;
  }
}
```

## 用户管理接口

### 1. 获取当前用户信息
#### 接口信息
- 路径: `/users/me`
- 方法: GET
- 权限: 需要认证

#### 响应数据
```json
{
  "id": 1,
  "email": "<EMAIL>",
  "username": "username",
  "role": "user",
  "is_active": true,
  "subscription_type": "monthly",
  "subscription_end_date": "2024-12-31T23:59:59Z",
  "permissions": ["read", "write"],
  "features": ["basic", "advanced"],
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-21T12:00:00Z"
}
```

#### Vue实现示例
```typescript
// api/user.ts
export const getUserInfo = async () => {
  try {
    const response = await axios.get('/api/v1/users/me');
    return response.data;
  } catch (error) {
    if (error.response?.status === 401) {
      // Token过期，跳转到登录页
      router.push('/login');
    }
    throw error;
  }
}

// 使用示例 (Composition API)
const userStore = useUserStore();

onMounted(async () => {
  try {
    const userInfo = await getUserInfo();
    userStore.setUserInfo(userInfo);
  } catch (error) {
    showError('获取用户信息失败');
  }
});
```

### 2. 更新用户信息
#### 接口信息
- 路径: `/users/{user_id}`
- 方法: PUT
- 权限: 管理员或本人

#### 请求参数
```json
{
  "email": "<EMAIL>",           // 可选
  "username": "newname",                // 可选
  "password": "newpassword",            // 可选
  "role": "admin",                      // 仅管理员可设置
  "is_active": true,                    // 仅管理员可设置
  "subscription_type": "yearly",        // 仅管理员可设置
  "subscription_end_date": "2024-12-31T23:59:59Z",  // 仅管理员可设置
  "permissions": ["read", "write"],     // 仅管理员可设置
  "features": ["basic", "advanced"]     // 仅管理员可设置
}
```

#### Vue实现示例
```typescript
// api/user.ts
export const updateUser = async (userId: number, userData: Partial<User>) => {
  try {
    const response = await axios.put(`/api/v1/users/${userId}`, userData);
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      throw new Error(error.response.data.detail);
    }
    throw error;
  }
}

// 使用示例
const handleUpdateUser = async () => {
  try {
    const updatedUser = await updateUser(userId, {
      subscription_type: selectedType.value,
      subscription_end_date: endDate.value
    });
    showSuccess('更新成功');
    refreshUserList();  // 刷新用户列表
  } catch (error) {
    showError(error.message);
  }
}
```

### 3. 删除用户
#### 接口信息
- 路径: `/users/{user_id}`
- 方法: DELETE
- 权限: 仅管理员

#### Vue实现示例
```typescript
// api/user.ts
export const deleteUser = async (userId: number) => {
  try {
    await axios.delete(`/api/v1/users/${userId}`);
    return true;
  } catch (error) {
    if (error.response?.status === 403) {
      throw new Error('没有删除权限');
    }
    throw error;
  }
}

// 使用示例
const handleDeleteUser = async (userId: number) => {
  if (await confirm('确定要删除此用户吗？')) {
    try {
      await deleteUser(userId);
      showSuccess('删除成功');
      refreshUserList();
    } catch (error) {
      showError(error.message);
    }
  }
}
```

## 数据导出导入接口

### 1. 导出用户数据
#### 接口信息
- 路径: `/users/export`
- 方法: GET
- 权限: 仅管理员
- 说明: 导出基础用户数据（不含密码）

#### 响应数据
```json
{
  "export_date": "2024-01-21T12:00:00Z",
  "total_users": 100,
  "users": [
    {
      "id": 1,
      "email": "<EMAIL>",
      "username": "username",
      "role": "user",
      "is_active": true,
      "subscription_type": "monthly",
      "created_at": "2024-01-01T00:00:00Z"
    }
    // ... 更多用户
  ]
}
```

#### Vue实现示例
```typescript
// api/export.ts
export const exportUsers = async () => {
  try {
    const response = await axios.get('/api/v1/users/export', {
      responseType: 'blob'  // 用于文件下载
    });
    
    // 创建下载
    const blob = new Blob([response.data], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `users_export_${new Date().toISOString()}.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    showError('导出失败');
    throw error;
  }
}
```

### 2. 导入用户数据
#### 接口信息
- 路径: `/users/import`
- 方法: POST
- 权限: 仅管理员
- 格式: multipart/form-data

#### 响应数据
```json
{
  "detail": "数据导入完成",
  "total": 100,
  "success": 95,
  "skipped": 3,
  "errors": 2,
  "error_details": [
    "用户 <EMAIL>: 邮箱已存在",
    "用户 <EMAIL>: 数据格式错误"
  ]
}
```

#### Vue实现示例
```typescript
// api/import.ts
export const importUsers = async (file: File) => {
  try {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await axios.post('/api/v1/users/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
    
    return response.data;
  } catch (error) {
    if (error.response?.status === 400) {
      throw new Error('导入文件格式错误');
    }
    throw error;
  }
}

// 使用示例
const handleFileUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;
  
  try {
    const result = await importUsers(file);
    showSuccess(`导入完成：成功 ${result.success} 条，失败 ${result.errors} 条`);
    if (result.error_details?.length) {
      showWarning(result.error_details.join('\n'));
    }
  } catch (error) {
    showError(error.message);
  }
}
```

## 统计分析接口

### 1. 获取用户统计信息
#### 接口信息
- 路径: `/users/stats`
- 方法: GET
- 权限: 仅管理员

#### 响应数据
```json
{
  "total_users": 100,
  "active_users": 80,
  "subscription_stats": {
    "none": 20,
    "monthly": 30,
    "yearly": 50
  },
  "registration_trend": {
    "2024-01": 20,
    "2024-02": 25,
    "2024-03": 30
  },
  "timestamp": "2024-01-21T12:00:00Z"
}
```

#### Vue实现示例
```typescript
// api/stats.ts
export const getUserStats = async () => {
  try {
    const response = await axios.get('/api/v1/users/stats');
    return response.data;
  } catch (error) {
    throw new Error('获取统计数据失败');
  }
}

// 使用示例（结合ECharts）
import * as echarts from 'echarts';

const renderStats = async () => {
  try {
    const stats = await getUserStats();
    
    // 渲染用户订阅饼图
    const subscriptionChart = echarts.init(document.getElementById('subscription-chart'));
    subscriptionChart.setOption({
      title: { text: '用户订阅分布' },
      series: [{
        type: 'pie',
        data: Object.entries(stats.subscription_stats).map(([type, count]) => ({
          name: type,
          value: count
        }))
      }]
    });
    
    // 渲染注册趋势图
    const trendChart = echarts.init(document.getElementById('trend-chart'));
    trendChart.setOption({
      title: { text: '用户注册趋势' },
      xAxis: { type: 'category', data: Object.keys(stats.registration_trend) },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        data: Object.values(stats.registration_trend)
      }]
    });
  } catch (error) {
    showError(error.message);
  }
}
```

## 功能与权限接口

### 1. 获取用户功能列表
#### 接口信息
- 路径: `/users/features`
- 方法: GET
- 权限: 需要认证

#### 响应数据
```json
{
  "features": [
    "basic_dashboard",
    "advanced_analytics",
    "export_data",
    "api_access"
  ],
  "subscription_features": {
    "basic": ["basic_dashboard"],
    "premium": ["advanced_analytics", "export_data"],
    "enterprise": ["api_access"]
  }
}
```

### 2. 检查功能权限
#### 接口信息
- 路径: `/users/features/check/{feature_name}`
- 方法: GET
- 权限: 需要认证

#### 响应数据
```json
{
  "feature": "advanced_analytics",
  "has_access": true,
  "reason": "premium_subscription"
}
```

#### Vue实现示例
```typescript
// composables/useFeatures.ts
export const useFeatures = () => {
  const checkFeatureAccess = async (featureName: string) => {
    try {
      const response = await axios.get(`/api/v1/users/features/check/${featureName}`);
      return response.data.has_access;
    } catch {
      return false;
    }
  };

  const getFeaturesList = async () => {
    try {
      const response = await axios.get('/api/v1/users/features');
      return response.data.features;
    } catch {
      return [];
    }
  };

  return {
    checkFeatureAccess,
    getFeaturesList
  };
};

// 使用示例
const { checkFeatureAccess } = useFeatures();

// 在组件中使用
const canAccessAnalytics = ref(false);
onMounted(async () => {
  canAccessAnalytics.value = await checkFeatureAccess('advanced_analytics');
});
```

## 系统配置

### 1. 系统环境变量
```python
# .env
DATABASE_URL=sqlite:///./sql_app.db
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256
```

### 2. 数据库配置
```python
# src/core/config.py
SQLALCHEMY_DATABASE_URL = "sqlite:///./sql_app.db"
DATABASE_CONNECT_DICT = {"check_same_thread": False}

# 数据库迁移
# alembic.ini
sqlalchemy.url = sqlite:///./sql_app.db

# 运行迁移
$ alembic revision --autogenerate -m "description"
$ alembic upgrade head
```

### 3. 安全配置
```python
# src/core/security.py
# JWT配置
SECRET_KEY = "your-secret-key-here"
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# 密码哈希配置
PWD_CONTEXT = CryptContext(schemes=["bcrypt"], deprecated="auto")
```

### 4. 角色与权限配置
```python
# src/core/constants.py
USER_ROLES = {
    "user": "普通用户",
    "admin": "管理员",
    "vip": "VIP用户",
    "premium": "高级用户"
}

SUBSCRIPTION_TYPES = {
    "none": "未订阅",
    "monthly": "月度订阅",
    "quarterly": "季度订阅",
    "yearly": "年度订阅",
    "lifetime": "终身会员"
}

# 订阅时长（天）
SUBSCRIPTION_DURATIONS = {
    "monthly": 30,
    "quarterly": 90,
    "yearly": 365,
    "lifetime": 36500  # 100年
}
```

## 部署说明

### 1. 开发环境
```bash
# 安装依赖
pip install -r requirements.txt

# 运行开发服务器
uvicorn src.main:app --reload --port 8000
```

### 2. 生产环境
```bash
# 生产环境配置
gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000

# 使用 supervisor 管理进程
[program:fastapi]
command=/path/to/venv/bin/gunicorn src.main:app -w 4 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000
directory=/path/to/project
user=www-data
autostart=true
autorestart=true
```

### 3. 数据库备份
```bash
# 自动备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d)
cp sql_app.db backups/sql_app_$DATE.db
find backups/ -name "sql_app_*.db" -mtime +7 -delete
```

## 性能优化建议

### 1. 数据库优化
- 添加适当的索引
- 使用异步数据库操作
- 实现查询缓存
- 定期清理过期数据

### 2. API优化
- 实现请求限流
- 添加响应缓存
- 使用异步处理大量数据
- 实现批量操作接口

### 3. 前端优化
- 实现数据缓存
- 使用延迟加载
- 添加请求队列
- 实现错误重试机制

## 安全建议

### 1. 密码安全
- 强制密码复杂度要求
- 定期密码过期提醒
- 密码重试次数限制
- 敏感操作二次验证

### 2. 数据安全
- 定期数据备份
- 敏感数据加密
- 访问日志记录
- 数据定期清理

### 3. 接口安全
- 实现 CORS 保护
- 添加 Rate Limiting
- 实现 API 签名验证
- 监控异常访问