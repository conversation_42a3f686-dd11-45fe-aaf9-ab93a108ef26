"""
FastAPI主应用文件
配置应用和路由
"""
from fastapi import FastAPI, APIRouter, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from starlette.exceptions import HTTPException as StarletteHTTPException
import os
import sys
import logging
from datetime import datetime

from src.config import settings
from src.api.v1.api import api_router, add_cors_middleware
from src.db.database import Base, engine, SessionLocal
from src.core.database_management import init_db, update_db
from src.init_db import init_db  # 导入初始化函数

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 导入所有模型以确保它们被注册
from src.models.user import User
from src.models.article import Article, ArticleImage
from src.models.password_reset_token import PasswordResetToken

def ensure_path_starts_with_slash(path: str) -> str:
    """确保路径以斜杠开头
    
    Args:
        path: 需要处理的路径
        
    Returns:
        处理后的路径，确保以斜杠开头
    """
    if not path:
        logger.warning("API路径为空，使用默认值'/'")
        return "/"
    
    normalized_path = f"/{path.lstrip('/')}"
    if normalized_path != path:
        logger.info(f"API路径已标准化: '{path}' -> '{normalized_path}'")
    return normalized_path

def create_app() -> FastAPI:
    """
    创建 FastAPI 应用
    """
    # 初始化数据库（创建所有表）
    init_db()  # 移除参数

    app = FastAPI(
        title=settings.PROJECT_NAME,
        description="北斗九号用户管理系统API",
        version="1.0.0",
        openapi_url=f"{settings.API_V1_STR}/openapi.json",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 配置 CORS
    if settings.BACKEND_CORS_ORIGINS:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

    # 注册路由
    app.include_router(api_router, prefix=settings.API_V1_STR)

    # 配置静态文件
    os.makedirs("static", exist_ok=True)
    os.makedirs("uploads", exist_ok=True)
    os.makedirs("uploads/avatars", exist_ok=True)
    os.makedirs("uploads/articles", exist_ok=True)

    app.mount("/static", StaticFiles(directory="static"), name="static")
    app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """请求日志中间件"""
        start_time = datetime.utcnow()
        response = await call_next(request)
        end_time = datetime.utcnow()
        duration = (end_time - start_time).total_seconds() * 1000
        logger.info(
            f"Path: {request.url.path} "
            f"Method: {request.method} "
            f"Status: {response.status_code} "
            f"Duration: {duration:.2f}ms"
        )
        return response

    return app

# 创建应用实例
app = create_app()

# 如果是直接运行此文件（用于初始化数据库），则退出
if __name__ == "__main__":
    logger.info("数据库初始化完成")
    sys.exit(0)