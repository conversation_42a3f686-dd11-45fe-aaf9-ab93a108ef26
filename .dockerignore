# Git
.git
.gitignore

# Python
**/__pycache__
**/*.pyc
**/*.pyo
**/.env
**/.git
**/.github
**/tests
**/local_settings.py
Dockerfile
docker-compose*
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg
venv/
myenv/

# IDE
.idea/
.vscode/
*.swp
*.swo

# 数据库
*.db
sql_app.db

# 环境配置
.env
.env.*
!.env.example

# 日志
*.log

# 上传文件
uploads/
media/

# 测试
.pytest_cache/
.coverage
htmlcov/

# 压缩包和备份
*.tar
*.tar.gz
*.tgz
*.zip
*.rar
*.7z
*.bak

# 其他
.DS_Store
Thumbs.db

# Additional files
README.md

# New additions from the code block
**.md 