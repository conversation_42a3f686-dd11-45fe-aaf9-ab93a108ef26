八字分析 API 文档
==============

基础信息
-------
Base URL: /api/v1/bazi-analysis
认证方式: Bearer Token
Content-Type: application/json

接口列表
-------

1. 创建八字分析
-------------
POST /

请求头：
Authorization: Bearer <token>

请求体：
{
    "birth_year": int,     // 必填，出生年份 (1900-2100)
    "birth_month": int,    // 必填，出生月份 (1-12)
    "birth_day": int,      // 必填，出生日期 (1-31)
    "birth_time": string,  // 可选，出生时间 (HH:mm 格式)
    "analysis_type": string,  // 可选，分析类型，最大长度50
    "notes": string        // 可选，备注信息，最大长度500
}

示例请求：
{
    "birth_year": 1990,
    "birth_month": 1,
    "birth_day": 1,
    "birth_time": "12:30",
    "analysis_type": "career",
    "notes": "希望重点分析事业发展"
}

响应：201 Created
{
    "id": 1,
    "birth_year": 1990,
    "birth_month": 1,
    "birth_day": 1,
    "birth_time": "12:30",
    "analysis_type": "career",
    "analysis_result": "根据您的八字分析，2024年事业运势...",
    "notes": "希望重点分析事业发展",
    "created_at": "2024-02-20T15:42:08",
    "updated_at": null
}

2. 获取八字分析列表
----------------
GET /

请求头：
Authorization: Bearer <token>

查询参数：
- skip: int, 默认值=0, 分页起始位置
- limit: int, 默认值=100, 每页记录数（1-100）

响应：200 OK
{
    "total": 10,  // 总记录数
    "items": [    // 分析记录列表
        {
            // 单条记录字段同上
        }
    ]
}

3. 获取指定分析
------------
GET /{analysis_id}

请求头：
Authorization: Bearer <token>

路径参数：
- analysis_id: int, 分析记录ID

响应：200 OK
{
    // 字段同创建响应
}

4. 更新分析
---------
PUT /{analysis_id}

请求头：
Authorization: Bearer <token>

路径参数：
- analysis_id: int, 分析记录ID

请求体：
{
    "analysis_type": string,  // 可选，分析类型
    "notes": string          // 可选，备注信息
}

示例请求：
{
    "analysis_type": "career",
    "notes": "补充说明：希望着重分析2024年运势"
}

响应：200 OK
{
    // 字段同创建响应
}

5. 删除分析
---------
DELETE /{analysis_id}

请求头：
Authorization: Bearer <token>

路径参数：
- analysis_id: int, 分析记录ID

响应：204 No Content

错误响应
-------
1. 401 Unauthorized
{
    "detail": "未登录或token已过期"
}

2. 403 Forbidden
{
    "detail": "无权访问此分析记录"
}

3. 404 Not Found
{
    "detail": "分析记录不存在或无权访问"
}

4. 422 Unprocessable Entity
{
    "detail": [
        {
            "loc": ["body", "birth_year"],
            "msg": "出生年份必须在1900到2100之间",
            "type": "value_error"
        }
    ]
}

权限说明
-------
1. 普通用户权限：
   - 只能查看/修改/删除自己创建的分析记录
   - 可以创建新的分析记录
   - 不能直接修改分析结果

2. 管理员权限：
   - 可以查看所有用户的分析记录
   - 可以修改/删除任何分析记录
   - 不能直接修改分析结果

字段说明
-------
1. birth_year: 出生年份
   - 必填
   - 范围：1900-2100
   - 示例：1990

2. birth_month: 出生月份
   - 必填
   - 范围：1-12
   - 示例：1

3. birth_day: 出生日期
   - 必填
   - 范围：1-31
   - 示例：1

4. birth_time: 出生时间
   - 可选
   - 格式：HH:mm（24小时制）
   - 示例："12:30"

5. analysis_type: 分析类型
   - 可选
   - 最大长度：50字符
   - 示例："career"（事业）

6. notes: 备注信息
   - 可选
   - 最大长度：500字符
   - 示例："希望重点分析事业发展"

7. analysis_result: 分析结果
   - 只读字段
   - 由系统生成
   - 不可直接修改

注意事项
-------
1. 所有请求必须携带有效的 Bearer Token
2. 时间相关字段均使用 UTC 时间
3. 分析结果由系统生成，不接受用户修改
4. 创建分析时会自动关联到当前登录用户
5. 删除操作不可恢复
6. 分页查询时建议使用合理的 limit 值，避免返回数据过大
7. 出生时间为可选项，但提供更精确的出生时间可以得到更准确的分析结果 