数据备份 API 文档
==============

基础信息
-------
Base URL: /api/v1/backup
权限要求：仅限超级管理员访问
认证方式：Bearer Token

API 端点
-------

1. 创建备份
----------
路径: /export
方法: POST
描述: 创建一个新的数据库备份

响应: 201 Created
{
    "id": 1,
    "filename": "backup_20240220_154208.zip",
    "size": 1024567,
    "created_at": "2024-02-20T15:42:08",
    "created_by": "<EMAIL>",
    "status": "completed",
    "checksum": "sha256:abc123..."
}

2. 获取备份列表
-------------
路径: /
方法: GET

查询参数：
- skip: int, 默认值=0, 分页起始位置
- limit: int, 默认值=10, 每页记录数

响应: 200 OK
{
    "total": 5,
    "items": [
        {
            "id": 1,
            "filename": "backup_20240220_154208.zip",
            "size": 1024567,
            "created_at": "2024-02-20T15:42:08",
            "created_by": "<EMAIL>",
            "status": "completed",
            "checksum": "sha256:abc123..."
        }
    ]
}

3. 下载备份
---------
路径: /{backup_id}/download
方法: GET
描述: 下载指定的备份文件

响应头：
Content-Type: application/zip
Content-Disposition: attachment; filename=backup_xxx.zip

响应体：
二进制文件流

4. 删除备份
---------
路径: /{backup_id}
方法: DELETE
描述: 删除指定的备份记录及文件

响应: 204 No Content

错误响应
-------

1. 401 Unauthorized
{
    "detail": "未登录或token已过期"
}

2. 403 Forbidden
{
    "detail": "仅超级管理员可以访问此接口"
}

3. 404 Not Found
{
    "detail": "备份记录不存在"
}

4. 500 Internal Server Error
{
    "detail": "备份创建失败：<错误详情>"
}

注意事项
-------
1. 所有操作仅限超级管理员执行
2. 备份创建是异步操作，需要轮询状态
3. 备份文件包含敏感数据，请妥善保管
4. 建议定期清理旧备份以节省存储空间
5. 下载大文件时支持断点续传
6. 所有操作都会被记录在系统日志中

字段说明
-------
id: 备份记录ID
filename: 备份文件名
size: 文件大小（字节）
created_at: 创建时间
created_by: 创建者邮箱
status: 备份状态（pending/processing/completed/failed）
checksum: 文件校验和（SHA256）

使用示例
-------
1. 创建新备份：
   curl -X POST 'http://api.example.com/api/v1/backup/export' \
   -H 'Authorization: Bearer <token>'

2. 获取备份列表：
   curl 'http://api.example.com/api/v1/backup?skip=0&limit=10' \
   -H 'Authorization: Bearer <token>'

3. 下载备份：
   curl 'http://api.example.com/api/v1/backup/1/download' \
   -H 'Authorization: Bearer <token>' \
   --output backup.zip

4. 删除备份：
   curl -X DELETE 'http://api.example.com/api/v1/backup/1' \
   -H 'Authorization: Bearer <token>' 