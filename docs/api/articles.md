# 文章管理 API 文档

## 基础信息
- 基础路径: `/api/v1/articles`
- 需要认证的接口会在描述中特别说明
- 认证方式: Bearer Token

## 接口列表

### 1. 创建文章
- 请求方法: `POST`
- 路径: `/`
- 权限: 仅管理员
- 请求头:
```
Authorization: Bearer <your_token>
Content-Type: application/json
```
- 请求体:
```json
{
    "title": "测试文章",          // 必填，至少1个字符
    "content": "文章内容...",     // 必填，至少50个字符
    "summary": "文章摘要",        // 可选
    "cover_image": "图片URL",     // 可选
    "category": "技术",          // 可选
    "tags": ["FastAPI", "Python"], // 可选
    "meta_title": "SEO标题",      // 可选
    "meta_description": "SEO描述", // 可选
    "is_featured": false,         // 可选，是否推荐
    "status": "draft",           // 可选，默认draft
    "slug": "test-article"       // 可选，自动生成
}
```

- 响应示例:
```json
{
    "id": 1,
    "title": "测试文章",
    "content": "文章内容...",
    "summary": "文章摘要",
    "cover_image": "图片URL",
    "category": "技术",
    "tags": ["FastAPI", "Python"],
    "meta_title": "SEO标题",
    "meta_description": "SEO描述",
    "is_featured": false,
    "status": "draft",
    "slug": "test-article",
    "author_id": 1,
    "article_metadata": {
        "views": 0,
        "likes": 0,
        "shares": 0,
        "comments": 0
    },
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z",
    "published_at": null
}
```

### 2. 获取文章列表
- 请求方法: `GET`
- 路径: `/`
- 权限: 公开（未登录用户只能看到已发布文章）
- 查询参数:
  - skip: 跳过条数（默认: 0）
  - limit: 每页条数（默认: 10）
  - category: 分类筛选（可选）
  - status: 状态筛选（可选）
  - author_id: 作者ID筛选（可选）
  - tag: 标签筛选（可选）
  - search: 搜索关键词（可选）
  - order_by: 排序字段（可选，支持: created_at|updated_at|view_count|like_count）
- 响应示例:
```json
{
    "total": 100,
    "items": [
        {
            "id": 1,
            "title": "文章标题",
            "summary": "文章摘要",
            "category": "文章分类",
            "tags": ["标签1", "标签2"],
            "status": "published",
            "slug": "article-url-slug",
            "author_id": 1,
            "view_count": 100,
            "like_count": 50,
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### 3. 获取文章统计信息
- 请求方法: `GET`
- 路径: `/stats`
- 权限: 仅管理员
- 响应示例:
```json
{
    "total_articles": 100,
    "published_articles": 80,
    "draft_articles": 15,
    "total_views": 10000,
    "total_likes": 5000,
    "total_comments": 2000
}
```

### 4. 获取指定文章
- 请求方法: `GET`
- 路径: `/{article_id}`
- 权限: 公开（未登录用户只能看到已发布文章）
- 查询参数:
  - increment_views: 是否增加浏览量（默认: true）
- 响应示例:
```json
{
    "id": 1,
    "title": "文章标题",
    "content": "文章内容",
    "summary": "文章摘要",
    "category": "文章分类",
    "tags": ["标签1", "标签2"],
    "status": "published",
    "slug": "article-url-slug",
    "author_id": 1,
    "view_count": 101,
    "like_count": 50,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

### 5. 通过 Slug 获取文章
- 请求方法: `GET`
- 路径: `/by-slug/{slug}`
- 权限: 公开（未登录用户只能看到已发布文章）
- 查询参数:
  - increment_views: 是否增加浏览量（默认: true）
- 响应格式同"获取指定文章"

### 6. 更新文章
- 请求方法: `PUT`
- 路径: `/{article_id}`
- 权限: 仅管理员
- 状态转换规则:
  - 草稿 -> 发布/归档
  - 发布 -> 归档/草稿
  - 归档 -> 草稿
- 说明:
  - 发布时自动设置发布时间
  - 归档时清除发布时间
- 请求头:
```
Authorization: Bearer <your_token>
Content-Type: application/json
```
- 请求体: 同创建文章，所有字段都是可选的
- 响应格式同"创建文章"

### 7. 删除文章
- 请求方法: `DELETE`
- 路径: `/{article_id}`
- 权限: 文章作者或管理员
- 请求头:
```
Authorization: Bearer <your_token>
```
- 响应: 204 No Content

### 8. 上传图片
- 请求方法: `POST`
- 路径: `/upload`
- 权限: 需要登录
- 请求头:
```
Authorization: Bearer <your_token>
Content-Type: multipart/form-data
```
- 请求参数:
  - file: 图片文件
  - article_id: 关联的文章ID（可选）
- 响应示例:
```json
{
    "id": 1,
    "article_id": 1,
    "filename": "example.jpg",
    "file_path": "/uploads/articles/example.jpg",
    "file_size": 1024,
    "mime_type": "image/jpeg",
    "created_at": "2024-01-01T00:00:00Z"
}
```

## 错误响应
所有错误响应的格式如下：
```json
{
    "detail": "错误信息描述"
}
```

### 常见错误码
- 400: 请求参数错误（如：无效的文章状态）
- 401: 未认证
- 403: 无权限
- 404: 文章不存在
- 422: 请求数据验证失败（如：标题长度不足）
- 500: 服务器内部错误

## 注意事项
1. 文章标题长度要求：5-200个字符
2. 文章内容长度要求：至少100个字符
3. slug格式：只允许小写字母、数字和连字符
4. 创建和更新时会自动处理slug重复问题
5. 统计信息（浏览量等）存储在article_metadata字段中
6. 图片上传限制:
   - 仅支持图片格式文件（image/*）
   - 建议图片大小不超过 5MB
7. 分页参数说明:
   - skip: 从0开始计数
   - limit: 建议不超过50
8. 时间格式均为 ISO 8601 格式