# FastAPI 用户管理系统

基于 FastAPI 和 SQLite 构建的用户管理系统，支持用户认证、权限管理等功能。

## 功能特点

- 用户认证和授权
- 基于 SQLite 的数据存储（可轻松切换到其他数据库）
- Docker 支持
- RESTful API 设计
- 完整的用户管理功能

## 技术栈

- Python 3.11
- FastAPI
- SQLAlchemy
- SQLite
- Docker & Docker Compose

## 快速开始

### 本地开发环境

1. 克隆项目
```bash
git clone https://github.com/your-username/your-repo-name.git
cd your-repo-name
```

2. 创建虚拟环境并安装依赖
```bash
python3.11 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

3. 初始化数据库
```bash
./init_db.sh  # Linux/Mac，初次部署会需要生成一个数据库
# 或
init_db.bat  # Windows
```

4. 启动应用
```bash
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### Docker 部署

1. 构建并启动容器
```bash
docker-compose up -d --build
```

2. 访问应用
```
http://localhost:4000/docs
```

## API 文档

启动应用后，可以通过以下地址访问 API 文档：

- Swagger UI: http://localhost:4000/docs
- ReDoc: http://localhost:4000/redoc

## 项目结构

```
.
├── src/
│   ├── api/            # API 路由
│   ├── core/           # 核心配置
│   ├── crud/           # 数据库操作
│   ├── db/             # 数据库配置
│   ├── models/         # 数据库模型
│   ├── schemas/        # Pydantic 模型
│   └── routers/        # 路由模块
├── tests/              # 测试文件
├── .env                # 环境变量
├── .gitignore         
├── docker-compose.yml  
├── Dockerfile         
├── init_db.sh         
├── main.py            
├── requirements.txt    
└── README.md         
```

## 环境变量配置

创建 `.env` 文件并配置以下环境变量：

```env
DATABASE_URL=sqlite:///./sql_app.db
SECRET_KEY=your-secret-key
```

## 部署说明

详细的部署说明请参考 [DOCKER_DEPLOY.md](DOCKER_DEPLOY.md)。

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。