# 基本配置
PROJECT_NAME="北斗九号用户管理系统"
API_V1_STR="/api/v1"

# 数据库配置
DATABASE_URL="sqlite:///./sql_app.db"
# 生产环境可以使用 PostgreSQL
# DATABASE_URL="postgresql://user:password@localhost:5432/dbname"

# JWT配置
# 生产环境请使用强密钥
SECRET_KEY="your-secret-key-here"
ALGORITHM="HS256"
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 超级管理员配置
FIRST_SUPERUSER="<EMAIL>"
FIRST_SUPERUSER_PASSWORD="change-this-password"

# CORS配置
# 生产环境请指定具体域名
BACKEND_CORS_ORIGINS='["http://localhost:3000", "https://your-domain.com"]'

# 前端URL配置
FRONTEND_URL="https://your-domain.com/"

# 邮件服务器配置
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# 密码重置配置
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=24

# 邮件配置
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="your-app-password"
MAIL_FROM="<EMAIL>"
MAIL_PORT=587
MAIL_SERVER="smtp.gmail.com"
MAIL_SSL_TLS=true

# 上传文件配置
UPLOAD_DIR="uploads"
MAX_UPLOAD_SIZE=5242880  # 5MB in bytes
ALLOWED_IMAGE_TYPES='["image/jpeg", "image/png", "image/gif", "image/webp"]'

# 其他配置
TZ=Asia/Shanghai 