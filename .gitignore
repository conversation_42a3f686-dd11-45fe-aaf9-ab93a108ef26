# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 虚拟环境
venv/
ENV/
env/

# IDE
.idea/
.vscode/
*.swp
*.swo

# 日志文件
logs/
*.log

# 数据库
*.db
*.sqlite3
sql_app.db

# 环境变量
.env

# 系统文件
.DS_Store
Thumbs.db

# 忽略所有 tar 压缩包
*.tar
*.tar.gz
*.tgz

# 或者指定特定目录的 tar 文件（可选）
# backups/*.tar
# data/*.tar.gz 